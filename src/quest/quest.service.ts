import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PARTICIPANT_STATUS,
  QuestEntity,
  QuestParticipantEntity,
} from 'src/models/quest-entity';
import { In, LessThan, Repository } from 'typeorm';
import { UserEntity } from 'src/models/user-entity';
import {
  GetAllQuestsResDTO,
  GetSingleQuestResDTO,
  QuestDTO,
} from './quest-dto';
import { QUEST_SCOPE, SUBMISSION_MEDIA_TYPES } from 'src/models/quest-entity/quest.entity';
import { CreateMCQQuestDTO } from './AI-quest/AI-quest-dto/AI-mcq.dto';
import { ProductQuestEntity } from 'src/models/products-summary-entity/product-quest.entity';

@Injectable()
export class QuestService {
  constructor(
    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(ProductQuestEntity)
    private readonly productQuestRepo: Repository<ProductQuestEntity>,
  ) {}

  async getAllEnterpriseQuests(user: UserEntity): Promise<GetAllQuestsResDTO> {
    
    const quests = await this.questRepo.find({
      where: {
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        isActive: true,
        isDeleted: false,
        numOfReports: LessThan(5),
      },
      relations: ['enterprise', 'creator', 'media', 'questType'],
      order: {
        isActive: 'DESC',
        createdAt: 'DESC',
      },
    });

    const filteredQuests = quests.filter(quest => {
      if (!quest.tags || quest.tags.length === 0) return true;
      return user.tags && quest.tags.some(tag => user.tags.includes(tag));
    });

    const productHuntQuests = await this.productQuestRepo.find({
      where: { isActive: true, isDeleted: false },
      order: { isActive: 'DESC', createdAt: 'DESC' },
    });

    const standardQuestDTOs = await Promise.all(
      filteredQuests.map(async (quest) => {
        let isCompleted = false;
        const participant = await this.participantRepo.findOne({
          where: {
            user: { id: user.id },
            quest: { id: quest.id },
          },
        });
        if (
          participant &&
          participant.status === PARTICIPANT_STATUS.COMPLETED
        ) {
          isCompleted = true;
        }
        return QuestDTO.transform(quest, true, isCompleted);
      })
    );

    const productHuntQuestDTOs = productHuntQuests.map((pq) => ({
      id: pq.id,
      isCompleted: false,
      title: pq.title,
      description: pq.description,
      completionCredits: pq.completionCredits || 0,
      startDate: pq.startDate || pq.createdAt,
      endDate: pq.endDate || pq.createdAt,
      questType: {
        id: pq.questTypeId || 0,
        name: 'Product Hunt',
        value: 'PRODUCT_QUEST',
        description: 'Product Hunt Quest',
      },
      difficulty: pq.difficulty || 'easy',
      createdAt: pq.createdAt,
      updatedAt: pq.updatedAt || pq.createdAt,
      isActive: pq.isActive,
      isDeleted: pq.isDeleted,
      submissionMediaType: pq.submissionType || 'text',
      creator: null,
      enterprise: null,
      media: [],
      participants: [],
      reports: [],
      scope: QUEST_SCOPE.AI,
      customQuestName: pq.title,
      isProductHunt: true,
    }));

    const allQuests = [...standardQuestDTOs, ...productHuntQuestDTOs].sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return {
      error: false,
      status : true,
      nbHits: allQuests.length,
      quests: allQuests,
    };
  }

  async getSingleEnterpriseQuest(
    user: UserEntity,
    questId: string,
  ): Promise<GetSingleQuestResDTO> {
    //
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        id: id,
      },
      relations: ['creator', 'enterprise', 'questType', 'media'],
    });

    if (!quest || quest.isDeleted === true) {
      throw new NotFoundException('Quest not found !!');
    }

    let isCompleted = false;

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: quest.id },
      },
    });

    if (participant && participant.status === PARTICIPANT_STATUS.COMPLETED) {
      isCompleted = true;
    }

    const questResponse = QuestDTO.transform(quest, true, isCompleted);

    return { error: false, quest: questResponse };
  }

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }
  async createMCQQuest(createDto: CreateMCQQuestDTO, user: UserEntity): Promise<QuestEntity> {
    if (!user.enterprise) {
      throw new BadRequestException('User must belong to an enterprise to create MCQ quests');
    }

    let endDate: Date;
    if (createDto.endDate) {
      endDate = new Date(createDto.endDate);
      if (isNaN(endDate.getTime())) {
        throw new BadRequestException('Invalid end date provided');
      }
      if (endDate <= new Date()) {
        throw new BadRequestException('End date must be in the future');
      }
    } else {
      endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); 
    }

    const quest = this.questRepo.create({
      title: createDto.title,
      description: createDto.description,
      difficulty: createDto.difficulty,
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE,
      isActive: true,
      startDate: new Date(),
      endDate: endDate,
      completionCredits: createDto.completionCredits || 100,
      enterprise: user.enterprise,
      creator: user,
      tags: createDto.tags || [] 
    });

    return await this.questRepo.save(quest);
  }
}
