import { <PERSON>du<PERSON> } from '@nestjs/common';
import { QuestService } from './quest.service';
import { QuestController } from './quest.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestMediaEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import { UserModule } from 'src/user/user.module';
import { AccessTokenEntity, UserEntity } from 'src/models/user-entity';
import { LoggerModule } from 'src/common/logger/logger.module';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { QuestInteractionService } from './quest-interaction/quest-interaction.service';
import { QuestInteractionController } from './quest-interaction/quest-interaction.controller';
import { FeedModule } from 'src/feed/feed.module';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import { AIQuestModule } from './AI-quest/AI-quest.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { AIQuestService } from './AI-quest/AI-quest.service';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { MCQGenerationService } from './AI-quest/mcq-generation.service';
import {
  UserMCQQuestionMetricsEntity,
  UserQuestMetricsEntity,
} from 'src/models/metrics-entity';
import { ProductQuestEntity } from 'src/models/products-summary-entity/product-quest.entity';
import { ProductQuestSubmissionEntity } from 'src/models/products-summary-entity/product-quest-submission.entity';
import { ProductMCQQuestEntity } from 'src/models/products-summary-entity/product-mcq-quest.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      QuestEntity,
      QuestMediaEntity,
      QuestParticipantEntity,
      AccessTokenEntity,
      UserEntity,
      QuestCompletionProofMediaEntity,
      QuestTypesEntity,
      LeaderboardEntity,
      UserCreditsEntity,
      MCQQuestionEntity,
      UserQuestMetricsEntity,
      UserMCQQuestionMetricsEntity,
      ProductQuestEntity,
      ProductQuestSubmissionEntity,
      ProductMCQQuestEntity,
    ]),
    UserModule,
    LoggerModule,
    FeedModule,
    AIQuestModule,
    ThirdPartyModule,
  ],
  providers: [
    QuestService,
    S3Service,
    AWSLlamaAIService,
    QuestInteractionService,
    LeaderboardService,
    AIQuestService,
    LeaderboardService,
    LeaderboardUtilsService,
    MCQGenerationService,
  ],
  controllers: [QuestController, QuestInteractionController],
  exports: [MCQGenerationService],
})
export class QuestModule {}
