import {
  Injectable,
  NestMiddleware,
  BadRequestException,
  InternalServerErrorException,
  Req,
  NotFoundException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Request, Response, NextFunction } from 'express';
import { Repository, LessThanOrEqual, Not, In } from 'typeorm';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import {
  QUEST_DIFFICULTY_TYPES,
  QuestEntity,
  QuestTypesEntity,
  QUEST_SCOPE,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import {
  AccessTokenEntity,
  EnterpriseEntity,
  UserEntity,
} from 'src/models/user-entity';
import { AIgeneratedQuestDetailsDto } from './AI-quest-dto';
import { AIQuestService } from './AI-quest.service';
import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';
import * as moment from 'moment';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AIQuestMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AIQuestMiddleware.name);

  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly awsLlamaAIService: AWSLlamaAIService,
    private readonly aiQuestService: AIQuestService,
    private readonly configService: ConfigService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,

    @InjectRepository(AiQuestsGenerationLogEntity)
    private readonly aiQuestsGenerationLogRepo: Repository<AiQuestsGenerationLogEntity>,

    @InjectRepository(AccessTokenEntity)
    private readonly accessTokenRepo: Repository<AccessTokenEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const user = await this.getUserFromToken(req);

      await this.removeNonCurrentWorkLocationQuests(user);

      await this.updateActiveQuestStatus(user);

      await this.findQuestWithSubmission(user);

      //   await this.awsLlamaAIService.generateContentFromAI(
      //     this.configService.get<string>('TEST_PROMPT'),
      //   );

      req['status'] = false;
      // Run the task in the background
      this.findOrGenerateQuestsForWorkLocation(user);

      // if (req['status'] == false) {
      //   const currentDate = new Date();
      //   currentDate.setHours(0, 0, 0, 0);

      //   // Filter out CUSTOM_QUEST types since they are not generated by AI
      //   const nonCustomQuestTypes = user.selectedQuestTypes.filter(
      //     (type) => type.value !== 'CUSTOM_QUEST'
      //   );

      //   const userTodaysQuests = await this.questRepo.find({
      //     where: {
      //       assignedToUser: { id: user.id },
      //       isDeleted: false,
      //       startDate: currentDate,
      //     },
      //   });

      //   const completedQuest = userTodaysQuests.find(
      //     (item) => item.isCompleted,
      //   );

      //   if (completedQuest) {
      //     const completedQuest = userTodaysQuests.find(
      //       (item) => item.isCompleted === true,
      //     );

      //     const questsAsPerUserCompletedQuestSettings =
      //       await this.questRepo.find({
      //         where: {
      //           assignedToUser: { id: user.id },
      //           isActive: true,
      //           isDeleted: false,
      //           questType: {
      //             id: In(nonCustomQuestTypes.map((type) => type.id)),
      //           },
      //           workLocation: completedQuest.workLocation,
      //           startDate: currentDate,
      //         },
      //       });

      //       console.log("status==0",             questsAsPerUserCompletedQuestSettings.length >=
      //       nonCustomQuestTypes.length)

      //     req['status'] =
      //       questsAsPerUserCompletedQuestSettings.length >=
      //       nonCustomQuestTypes.length;
      //   } else {
      //     const questsAsPerUserSettings = await this.questRepo.find({
      //       where: {
      //         assignedToUser: { id: user.id },
      //         isActive: true,
      //         isDeleted: false,
      //         questType: {
      //           id: In(nonCustomQuestTypes.map((type) => type.id)),
      //         },
      //         startDate: currentDate,
      //       },
      //     });

      //      console.log("status==0",             questsAsPerUserSettings.length >=
      //       nonCustomQuestTypes.length)

      //     req['status'] =
      //       questsAsPerUserSettings.length >= nonCustomQuestTypes.length;
      //   }
      // }

      if (req['status'] === false) {
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);

  // Exclude CUSTOM_QUEST from the selected types
  const nonCustomQuestTypes = user.selectedQuestTypes.filter(
    (type) => type.value !== 'CUSTOM_QUEST'
  );

  const userTodaysQuests = await this.questRepo.find({
    where: {
      assignedToUser: { id: user.id },
      isDeleted: false,
      startDate: currentDate,
    },
    relations: ['questType'],
  });

  // Filter only the quests that are active and match non-custom quest types
  const activeNonCustomQuests = userTodaysQuests.filter(
    (quest) =>
      quest.isActive &&
      nonCustomQuestTypes.some((t) => t.id === quest.questType.id)
  );

  // If all non-custom quest types are fulfilled with active quests, set status true
  req['status'] = activeNonCustomQuests.length >= nonCustomQuestTypes.length;


}

      next(); // Proceed to the API route
    } catch (error) {
      req['error'] = true;
      next();
    }
  }

  // -----------------------Internal Methods-----------------------

  private async removeNonCurrentWorkLocationQuests(
    user: UserEntity,
  ): Promise<void> {
    await this.questRepo.update(
      {
        assignedToUser: { id: user.id },
        scope: QUEST_SCOPE.AI,
        workLocation: Not(user.workLocation),
      },
      {
        isActive: false,
      },
    );
  }

  private async updateActiveQuestStatus(user: UserEntity) {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    // Update quests to start - only for current work location
    const questsToStart = await this.questRepo.find({
      where: {
        assignedToUser: { id: user.id },
        isDeleted: false,
        isActive: false,
        workLocation: user.workLocation,
        startDate: LessThanOrEqual(currentDate),
      },
    });

    for (const quest of questsToStart) {
      quest.isActive = true;
      await this.questRepo.save(quest);
    }

    // Update quests to end - only for current work location
    const questsToEnd = await this.questRepo.find({
      where: {
        assignedToUser: { id: user.id },
        isDeleted: false,
        isActive: true,
        workLocation: user.workLocation,
        endDate: LessThanOrEqual(currentDate),
      },
    });

    for (const quest of questsToEnd) {
      quest.isActive = false;
      quest.isDeleted = true;
      await this.questRepo.save(quest);
    }
  }

  private async findQuestWithSubmission(
    user: UserEntity,
  ): Promise<QuestEntity | null> {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    const submittedQuests: QuestEntity[] = [];

    // Find all quests for the current date and user
    const allQuests = await this.questRepo.find({
      where: {
        assignedToUser: { id: user.id },
        startDate: currentDate,
      },
      relations: ['questType', 'assignedToUser', 'enterprise'],
    });

    // Check submissions for all quests
    for (const quest of allQuests) {
      const submissionMedia = await this.aiQuestService.getQuestSubmissionMedia(
        user,
        quest.id.toString(),
      );

      if (submissionMedia.caption) {
        // Activate the quest if it's not already active
        if (!quest.isActive) {
          quest.isActive = true;
          await this.questRepo.save(quest);
        }
        submittedQuests.push(quest);
      }
    }

    // Return null if no submitted quests, allowing further processing
    return submittedQuests.length > 0 ? null : null;
  }

  private async findOrGenerateQuestsForWorkLocation(
    user: UserEntity,
  ): Promise<void> {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    const questsToReturn: QuestEntity[] = [];
    const processedQuestTypes = new Set<number>();

    // First, check for existing quests with submission for all quest types
    for (const selectedQuestType of user.selectedQuestTypes) {
      const existingQuests = await this.questRepo.find({
        where: {
          assignedToUser: { id: user.id },
          questType: { id: selectedQuestType.id },
          isDeleted: false,
          workLocation: user.workLocation,
          startDate: currentDate,
        },
        relations: ['questType', 'assignedToUser', 'enterprise'],
      });

      let questFound = false;

      // Check for submitted quests
      for (const quest of existingQuests) {
        const submissionMedia =
          await this.aiQuestService.getQuestSubmissionMedia(
            user,
            quest.id.toString(),
          );

        if (submissionMedia.caption) {
          if (!quest.isActive) {
            quest.isActive = true;
            await this.questRepo.save(quest);
          }
          questsToReturn.push(quest);
          processedQuestTypes.add(selectedQuestType.id);
          questFound = true;
          break;
        }
      }

      // If no submitted quest, check for existing location-based quests
      if (!questFound) {
        const locationQuests = await this.questRepo.find({
          where: {
            assignedToUser: { id: user.id },
            questType: { id: selectedQuestType.id },
            isDeleted: false,
            workLocation: user.workLocation,
            startDate: currentDate,
          },
          relations: ['questType', 'assignedToUser', 'enterprise'],
        });

        if (locationQuests.length > 0) {
          const quest = locationQuests[0];
          if (!quest.isActive) {
            quest.isActive = true;
            await this.questRepo.save(quest);
          }
          questsToReturn.push(quest);
          processedQuestTypes.add(selectedQuestType.id);
        }
      }
    }

    const currentDateFormatted = moment().format('YYYY-MM-DD');

    const isEntryLogExists = await this.aiQuestsGenerationLogRepo.findOne({
      where: {
        user: { id: user.id },
        currentDate: currentDateFormatted,
      },
    });

    if (!isEntryLogExists) {
      let new_entry_log: AiQuestsGenerationLogEntity;

      try {
        new_entry_log = await this.aiQuestsGenerationLogRepo.save({
          user: user,
          currentDate: currentDateFormatted,
          workLocation: user.workLocation,
        });        // Generate new quests for any unprocessed quest types
        // Exclude CUSTOM_QUEST types from AI generation as they are enterprise-level quests created manually by HR
        for (const selectedQuestType of user.selectedQuestTypes) {
          if (!processedQuestTypes.has(selectedQuestType.id)) {
            // Skip CUSTOM_QUEST types - they should only be created manually by HR, not auto-generated by AI
            if (selectedQuestType.value === 'CUSTOM_QUEST') {
              continue;
            }

            const generatedQuest =
              await this.awsLlamaAIService.generateQuestSuggestion(
                [selectedQuestType],
                user.difficulty,
                user.workLocation,
              );

            if (!generatedQuest || generatedQuest.length === 0) {
              await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
            }

            const savedQuest = await this.createAndSaveAIQuests(
              generatedQuest,
              user,
            );
            questsToReturn.push(savedQuest[0]);
          }
        }
      } catch (error) {
        if (new_entry_log != null) {
          await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
        }

        // throw error;
      }

      if (new_entry_log != null) {
        await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
      }
    } else {
      console.log(
        '===NOT ABLE TO GENERATE AI QUESTS BECAUSE ENTRY FOR USER ALREADY EXISTS IN LOG TABLE===',
      );
    }
  }

  private async createAndSaveAIQuests(
    AIQuests: AIgeneratedQuestDetailsDto[],
    user: UserEntity,
  ): Promise<QuestEntity[]> {
    return Promise.all(
      AIQuests.map(async (item) => {
        const AIQuest = new QuestEntity();

        AIQuest.scope = QUEST_SCOPE.AI;
        AIQuest.completionCredits = 50;
        AIQuest.title = item.questTitle;
        // AIQuest.description = item.questDescription;

        // Handle both string and array types for description
        AIQuest.description = Array.isArray(item.questDescription)
          ? item.questDescription.join('-$$- ')
          : item.questDescription;

        AIQuest.assignedToUser = user;
        AIQuest.workLocation = user.workLocation;

        AIQuest.difficulty =
          user.difficulty || QUEST_DIFFICULTY_TYPES.INTERMEDIATE;

        AIQuest.isActive = item.isActive;

        const startDate = new Date();
        startDate.setHours(0, 0, 0, 0);

        const nextStartDate = new Date(startDate);
        nextStartDate.setDate(nextStartDate.getDate() + 1);

        AIQuest.startDate = AIQuest.isActive ? startDate : nextStartDate;

        const nextEndDate = new Date(nextStartDate);
        nextEndDate.setDate(nextEndDate.getDate() + 1);
        AIQuest.endDate = AIQuest.isActive ? nextStartDate : nextEndDate;

        const questType = await this.questTypeRepo.findOne({
          where: { value: item.questTypeValue },
        });

        if (!questType) {
          throw new BadRequestException('Invalid QuestType provided.');
        }

        AIQuest.questType = questType;

        AIQuest.submissionMediaType =
          questType.value === 'FITNESS_QUEST'
            ? SUBMISSION_MEDIA_TYPES.MIXED
            : SUBMISSION_MEDIA_TYPES[item.proofOfCompletion.toUpperCase()];

        AIQuest.enterprise = user.enterprise;

        const savedQuest = await this.questRepo.save(AIQuest);

        const enterprise = await this.enterpriseRepo.findOneBy({
          id: user.enterprise.id,
        });
        enterprise.numOfQuests++;
        await this.enterpriseRepo.save(enterprise);

        return savedQuest;
      }),
    );
  }

  async getUserFromToken(@Req() req: Request): Promise<UserEntity> {
    const authHeader = req.headers.authorization;
    const token = authHeader.split(' ')[1];

    const accessToken = await this.accessTokenRepo.findOne({
      where: { isDeleted: false, accessToken: token },
      relations: ['user', 'user.enterprise', 'user.selectedQuestTypes'],
    });

    if (!accessToken) {
      throw new NotFoundException('Access token not found');
    }

    if (!accessToken.user.isActive) {
      throw new UnauthorizedException(
        'Your account is inactive. Please contact support for assistance.',
      );
    }

    if (!accessToken.user.isAccountVerified) {
      throw new UnauthorizedException(
        'Your account is not verified. Please verify your account first.',
      );
    }

    return accessToken.user;
  }
}




























// import {
//   Injectable,
//   NestMiddleware,
//   BadRequestException,
//   InternalServerErrorException,
//   Req,
//   NotFoundException,
//   UnauthorizedException,
// } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Request, Response, NextFunction } from 'express';
// import { Repository, LessThanOrEqual, Not, In } from 'typeorm';
// import { UserProfileService } from 'src/user/user-profile/user-profile.service';
// import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
// import {
//   QUEST_DIFFICULTY_TYPES,
//   QuestEntity,
//   QuestTypesEntity,
//   QUEST_SCOPE,
//   SUBMISSION_MEDIA_TYPES,
// } from 'src/models/quest-entity';
// import {
//   AccessTokenEntity,
//   EnterpriseEntity,
//   UserEntity,
// } from 'src/models/user-entity';
// import { AIgeneratedQuestDetailsDto } from './AI-quest-dto';
// import { AIQuestService } from './AI-quest.service';
// import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';
// import * as moment from 'moment';
// import { ConfigService } from '@nestjs/config';

// @Injectable()
// export class AIQuestMiddleware implements NestMiddleware {
//   constructor(
//     private readonly userProfileService: UserProfileService,
//     private readonly awsLlamaAIService: AWSLlamaAIService,
//     private readonly aiQuestService: AIQuestService,
//     private readonly configService: ConfigService,

//     @InjectRepository(QuestEntity)
//     private readonly questRepo: Repository<QuestEntity>,

//     @InjectRepository(QuestTypesEntity)
//     private readonly questTypeRepo: Repository<QuestTypesEntity>,

//     @InjectRepository(AiQuestsGenerationLogEntity)
//     private readonly aiQuestsGenerationLogRepo: Repository<AiQuestsGenerationLogEntity>,

//     @InjectRepository(AccessTokenEntity)
//     private readonly accessTokenRepo: Repository<AccessTokenEntity>,

//     @InjectRepository(EnterpriseEntity)
//     private readonly enterpriseRepo: Repository<EnterpriseEntity>,
//   ) {}

//   async use(req: Request, res: Response, next: NextFunction) {
//     try {
//       const user = await this.getUserFromToken(req);

//       await this.removeNonCurrentWorkLocationQuests(user);

//       await this.updateActiveQuestStatus(user);

//       await this.findQuestWithSubmission(user);

//       //   await this.awsLlamaAIService.generateContentFromAI(
//       //     this.configService.get<string>('TEST_PROMPT'),
//       //   );

//       req['status'] = false;
//       // Run the task in the background
//       this.findOrGenerateQuestsForWorkLocation(user);

//       if (req['status'] == false) {
//         const currentDate = new Date();
//         currentDate.setHours(0, 0, 0, 0);

//         const userTodaysQuests = await this.questRepo.find({
//           where: {
//             assignedToUser: { id: user.id },
//             isDeleted: false,
//             startDate: currentDate,
//           },
//         });

//         const completedQuest = userTodaysQuests.find(
//           (item) => item.isCompleted,
//         );

//         if (completedQuest) {
//           const completedQuest = userTodaysQuests.find(
//             (item) => item.isCompleted === true,
//           );

//           const questsAsPerUserCompletedQuestSettings =
//             await this.questRepo.find({
//               where: {
//                 assignedToUser: { id: user.id },
//                 isActive: true,
//                 isDeleted: false,
//                 questType: {
//                   id: In(user.selectedQuestTypes.map((type) => type.id)),
//                 },
//                 workLocation: completedQuest.workLocation,
//                 startDate: currentDate,
//               },
//             });

//           req['status'] =
//             questsAsPerUserCompletedQuestSettings.length >=
//             user.selectedQuestTypes.length;
//         } else {
//           const questsAsPerUserSettings = await this.questRepo.find({
//             where: {
//               assignedToUser: { id: user.id },
//               isActive: true,
//               isDeleted: false,
//               questType: {
//                 id: In(user.selectedQuestTypes.map((type) => type.id)),
//               },
//               startDate: currentDate,
//             },
//           });

//           req['status'] =
//             questsAsPerUserSettings.length >= user.selectedQuestTypes.length;
//         }
//       }

//       next(); // Proceed to the API route
//     } catch (error) {
//       req['error'] = true;
//       next();
//     }
//   }

//   // -----------------------Internal Methods-----------------------

//   private async removeNonCurrentWorkLocationQuests(
//     user: UserEntity,
//   ): Promise<void> {
//     await this.questRepo.update(
//       {
//         assignedToUser: { id: user.id },
//         scope: QUEST_SCOPE.AI,
//         workLocation: Not(user.workLocation),
//       },
//       {
//         isActive: false,
//       },
//     );
//   }

//   private async updateActiveQuestStatus(user: UserEntity) {
//     const currentDate = new Date();
//     currentDate.setHours(0, 0, 0, 0);

//     // Update quests to start - only for current work location
//     const questsToStart = await this.questRepo.find({
//       where: {
//         assignedToUser: { id: user.id },
//         isDeleted: false,
//         isActive: false,
//         workLocation: user.workLocation,
//         startDate: LessThanOrEqual(currentDate),
//       },
//     });

//     for (const quest of questsToStart) {
//       quest.isActive = true;
//       await this.questRepo.save(quest);
//     }

//     // Update quests to end - only for current work location
//     const questsToEnd = await this.questRepo.find({
//       where: {
//         assignedToUser: { id: user.id },
//         isDeleted: false,
//         isActive: true,
//         workLocation: user.workLocation,
//         endDate: LessThanOrEqual(currentDate),
//       },
//     });

//     for (const quest of questsToEnd) {
//       quest.isActive = false;
//       quest.isDeleted = true;
//       await this.questRepo.save(quest);
//     }
//   }

//   private async findQuestWithSubmission(
//     user: UserEntity,
//   ): Promise<QuestEntity | null> {
//     const currentDate = new Date();
//     currentDate.setHours(0, 0, 0, 0);

//     const submittedQuests: QuestEntity[] = [];

//     // Find all quests for the current date and user
//     const allQuests = await this.questRepo.find({
//       where: {
//         assignedToUser: { id: user.id },
//         startDate: currentDate,
//       },
//       relations: ['questType', 'assignedToUser', 'enterprise'],
//     });

//     // Check submissions for all quests
//     for (const quest of allQuests) {
//       const submissionMedia = await this.aiQuestService.getQuestSubmissionMedia(
//         user,
//         quest.id.toString(),
//       );

//       if (submissionMedia.caption) {
//         // Activate the quest if it's not already active
//         if (!quest.isActive) {
//           quest.isActive = true;
//           await this.questRepo.save(quest);
//         }
//         submittedQuests.push(quest);
//       }
//     }

//     // Return null if no submitted quests, allowing further processing
//     return submittedQuests.length > 0 ? null : null;
//   }

//   private async findOrGenerateQuestsForWorkLocation(
//     user: UserEntity,
//   ): Promise<void> {
//     const currentDate = new Date();
//     currentDate.setHours(0, 0, 0, 0);

//     const questsToReturn: QuestEntity[] = [];
//     const processedQuestTypes = new Set<number>();

//     // First, check for existing quests with submission for all quest types
//     for (const selectedQuestType of user.selectedQuestTypes) {
//       const existingQuests = await this.questRepo.find({
//         where: {
//           assignedToUser: { id: user.id },
//           questType: { id: selectedQuestType.id },
//           isDeleted: false,
//           workLocation: user.workLocation,
//           startDate: currentDate,
//         },
//         relations: ['questType', 'assignedToUser', 'enterprise'],
//       });

//       let questFound = false;

//       // Check for submitted quests
//       for (const quest of existingQuests) {
//         const submissionMedia =
//           await this.aiQuestService.getQuestSubmissionMedia(
//             user,
//             quest.id.toString(),
//           );

//         if (submissionMedia.caption) {
//           if (!quest.isActive) {
//             quest.isActive = true;
//             await this.questRepo.save(quest);
//           }
//           questsToReturn.push(quest);
//           processedQuestTypes.add(selectedQuestType.id);
//           questFound = true;
//           break;
//         }
//       }

//       // If no submitted quest, check for existing location-based quests
//       if (!questFound) {
//         const locationQuests = await this.questRepo.find({
//           where: {
//             assignedToUser: { id: user.id },
//             questType: { id: selectedQuestType.id },
//             isDeleted: false,
//             workLocation: user.workLocation,
//             startDate: currentDate,
//           },
//           relations: ['questType', 'assignedToUser', 'enterprise'],
//         });

//         if (locationQuests.length > 0) {
//           const quest = locationQuests[0];
//           if (!quest.isActive) {
//             quest.isActive = true;
//             await this.questRepo.save(quest);
//           }
//           questsToReturn.push(quest);
//           processedQuestTypes.add(selectedQuestType.id);
//         }
//       }
//     }

//     const currentDateFormatted = moment().format('YYYY-MM-DD');

//     const isEntryLogExists = await this.aiQuestsGenerationLogRepo.findOne({
//       where: {
//         user: { id: user.id },
//         currentDate: currentDateFormatted,
//       },
//     });

//     if (!isEntryLogExists) {
//       let new_entry_log: AiQuestsGenerationLogEntity;

//       try {
//         new_entry_log = await this.aiQuestsGenerationLogRepo.save({
//           user: user,
//           currentDate: currentDateFormatted,
//           workLocation: user.workLocation,
//         });

//         // Generate new quests for any unprocessed quest types
//         for (const selectedQuestType of user.selectedQuestTypes) {
//           if (!processedQuestTypes.has(selectedQuestType.id)) {
//             const generatedQuest =
//               await this.awsLlamaAIService.generateQuestSuggestion(
//                 [selectedQuestType],
//                 user.difficulty,
//                 user.workLocation,
//               );

//             if (!generatedQuest || generatedQuest.length === 0) {
//               await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
//             }

//             const savedQuest = await this.createAndSaveAIQuests(
//               generatedQuest,
//               user,
//             );
//             questsToReturn.push(savedQuest[0]);
//           }
//         }
//       } catch (error) {
//         if (new_entry_log != null) {
//           await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
//         }

//         // throw error;
//       }

//       if (new_entry_log != null) {
//         await this.aiQuestsGenerationLogRepo.delete(new_entry_log);
//       }
//     } else {
//       console.log(
//         '===NOT ABLE TO GENERATE AI QUESTS BECAUSE ENTRY FOR USER ALREADY EXISTS IN LOG TABLE===',
//       );
//     }
//   }

//   private async createAndSaveAIQuests(
//     AIQuests: AIgeneratedQuestDetailsDto[],
//     user: UserEntity,
//   ): Promise<QuestEntity[]> {
//     return Promise.all(
//       AIQuests.map(async (item) => {
//         const AIQuest = new QuestEntity();

//         AIQuest.scope = QUEST_SCOPE.AI;
//         AIQuest.completionCredits = 50;
//         AIQuest.title = item.questTitle;
//         // AIQuest.description = item.questDescription;

//         // Handle both string and array types for description
//         AIQuest.description = Array.isArray(item.questDescription)
//           ? item.questDescription.join('-$$- ')
//           : item.questDescription;

//         AIQuest.assignedToUser = user;
//         AIQuest.workLocation = user.workLocation;

//         AIQuest.difficulty =
//           user.difficulty || QUEST_DIFFICULTY_TYPES.INTERMEDIATE;

//         AIQuest.isActive = item.isActive;

//         const startDate = new Date();
//         startDate.setHours(0, 0, 0, 0);

//         const nextStartDate = new Date(startDate);
//         nextStartDate.setDate(nextStartDate.getDate() + 1);

//         AIQuest.startDate = AIQuest.isActive ? startDate : nextStartDate;

//         const nextEndDate = new Date(nextStartDate);
//         nextEndDate.setDate(nextEndDate.getDate() + 1);
//         AIQuest.endDate = AIQuest.isActive ? nextStartDate : nextEndDate;

//         const questType = await this.questTypeRepo.findOne({
//           where: { value: item.questTypeValue },
//         });

//         if (!questType) {
//           throw new BadRequestException('Invalid QuestType provided.');
//         }

//         AIQuest.questType = questType;

//         AIQuest.submissionMediaType =
//           questType.value === 'FITNESS_QUEST'
//             ? SUBMISSION_MEDIA_TYPES.MIXED
//             : SUBMISSION_MEDIA_TYPES[item.proofOfCompletion.toUpperCase()];

//         AIQuest.enterprise = user.enterprise;

//         const savedQuest = await this.questRepo.save(AIQuest);

//         const enterprise = await this.enterpriseRepo.findOneBy({
//           id: user.enterprise.id,
//         });
//         enterprise.numOfQuests++;
//         await this.enterpriseRepo.save(enterprise);

//         return savedQuest;
//       }),
//     );
//   }

//   async getUserFromToken(@Req() req: Request): Promise<UserEntity> {
//     const authHeader = req.headers.authorization;
//     const token = authHeader.split(' ')[1];

//     const accessToken = await this.accessTokenRepo.findOne({
//       where: { isDeleted: false, accessToken: token },
//       relations: ['user', 'user.enterprise', 'user.selectedQuestTypes'],
//     });

//     if (!accessToken) {
//       throw new NotFoundException('Access token not found');
//     }

//     if (!accessToken.user.isActive) {
//       throw new UnauthorizedException(
//         'Your account is inactive. Please contact support for assistance.',
//       );
//     }

//     if (!accessToken.user.isAccountVerified) {
//       throw new UnauthorizedException(
//         'Your account is not verified. Please verify your account first.',
//       );
//     }

//     return accessToken.user;
//   }
// }