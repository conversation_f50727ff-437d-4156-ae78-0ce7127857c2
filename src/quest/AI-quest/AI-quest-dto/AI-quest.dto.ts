import { ApiProperty } from '@nestjs/swagger';

export class SubmitProductHuntQuestResponseDto {
  @ApiProperty({ description: 'Success status' })
  error: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Score achieved (for MCQ quests)', required: false })
  score?: number;

  @ApiProperty({ description: 'Number of correct answers (for MCQ quests)', required: false })
  correctAnswers?: number;

  @ApiProperty({ description: 'Total number of questions (for MCQ quests)', required: false })
  totalQuestions?: number;

  @ApiProperty({ description: 'Whether the quest was passed (for MCQ quests)', required: false })
  passed?: boolean;

  @ApiProperty({ description: 'Credits earned', required: false })
  creditsEarned?: number;
} 