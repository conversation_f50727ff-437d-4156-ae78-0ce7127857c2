import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AIQuestController } from './AI-quest.controller';
import { LoggerModule } from 'src/common/logger/logger.module';
import { UserModule } from 'src/user/user.module';
import {
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import {
  AccessTokenEntity,
  EnterpriseEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIQuestMiddleware } from './AI-quest.middleware';
import { AIQuestService } from './AI-quest.service';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { MCQGenerationService } from './mcq-generation.service';
import {
  UserMCQQuestionMetricsEntity,
  UserQuestMetricsEntity,
} from 'src/models/metrics-entity';
import { ProductQuestEntity } from 'src/models/products-summary-entity/product-quest.entity';
import { ProductQuestSubmissionEntity } from 'src/models/products-summary-entity/product-quest-submission.entity';
import { ProductMCQQuestEntity } from 'src/models/products-summary-entity/product-mcq-quest.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AccessTokenEntity,
      UserEntity,
      QuestEntity,
      QuestTypesEntity,
      QuestCompletionProofMediaEntity,
      LeaderboardEntity,
      UserCreditsEntity,
      AiQuestsGenerationLogEntity,
      QuestParticipantEntity,
      EnterpriseEntity,
      MCQQuestionEntity,
      UserQuestMetricsEntity,
      UserMCQQuestionMetricsEntity,
      ProductQuestEntity,
      ProductQuestSubmissionEntity,
      ProductMCQQuestEntity,
    ]),
    ThirdPartyModule,
    UserModule,
    LoggerModule,
  ],
  controllers: [AIQuestController],
  providers: [
    AIQuestService,
    LeaderboardService,
    LeaderboardUtilsService,
    MCQGenerationService,
  ],
})
export class AIQuestModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AIQuestMiddleware)
      .forRoutes({ path: 'quests', method: RequestMethod.GET });
  }
}
