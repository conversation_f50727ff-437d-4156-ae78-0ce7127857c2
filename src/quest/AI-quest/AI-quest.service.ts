import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { DataSource, EntityManager, Repository, In, Between } from 'typeorm';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import {
  PARTICIPANT_STATUS,
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from 'src/models/user-entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import { UserCreditsDTO } from './AI-quest-dto/UserCredits.dto';
import { UserCreditsEntity } from 'src/models/credits-entity';
import {
  AIQuestDTO,
  CreateAIQuestResDTO,
  GetAIQuestSubmissionMediaResDTO,
  getSingleAIQuestByIdResDTO,
  submitAIQuestReqDTO,
  SubmitAIQuestResDTO,
} from './AI-quest-dto';
import {
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  QuestCompletionTrackingEntity,
} from 'src/models/quest-entity';
import { EnterpriseEntity } from '../../models/user-entity/enterprise.entity';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { Request } from 'express';
import { QuestDTO } from '../quest-dto';
import { GetAllUserQuestsQueryFilterInterface } from './interfaces';
import { CreateMCQQuestDTO, MCQSubmissionDTO } from './AI-quest-dto/AI-mcq.dto';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import {
  UserMCQQuestionMetricsEntity,
  UserQuestMetricsEntity,
} from 'src/models/metrics-entity';
import {
  ProductQuestSubmissionEntity,
  PRODUCT_QUEST_SUBMISSION_TYPE,
} from '../../models/products-summary-entity/product-quest-submission.entity';
import { ProductMCQQuestEntity } from '../../models/products-summary-entity/product-mcq-quest.entity';
import { ProductQuestEntity } from '../../models/products-summary-entity/product-quest.entity';

@Injectable()
export class AIQuestService {
  constructor(
    private readonly s3Service: S3Service,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLogger,
    private readonly leaderboardUtilsService: LeaderboardUtilsService,
    private readonly leaderboardService: LeaderboardService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionMediaRepo: Repository<QuestCompletionProofMediaEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,

    @InjectRepository(UserQuestMetricsEntity)
    private readonly userQuestMetricsRepo: Repository<UserQuestMetricsEntity>,

    @InjectRepository(UserMCQQuestionMetricsEntity)
    private readonly userMCQQuestionMetricsRepo: Repository<UserMCQQuestionMetricsEntity>,

    @InjectRepository(ProductQuestSubmissionEntity)
    private readonly productQuestSubmissionRepo: Repository<ProductQuestSubmissionEntity>,

    @InjectRepository(ProductMCQQuestEntity)
    private readonly productMCQQuestRepo: Repository<ProductMCQQuestEntity>,

    @InjectRepository(ProductQuestEntity)
    private readonly productQuestRepo: Repository<ProductQuestEntity>,

    private readonly aWSLlamaAIService: AWSLlamaAIService,
  ) {}

  async getAllUserQuests(
    user: UserEntity,
    req: Request,
    filterData: GetAllUserQuestsQueryFilterInterface,
  ): Promise<CreateAIQuestResDTO> {
    const { scope } = filterData;

    try {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      const activeAIquestsForToday = await this.questRepo.find({
        where: {
          assignedToUser: { id: user.id },
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
          startDate: currentDate,
        },
        relations: [
          'assignedToUser',
          'enterprise',
          'questType',
          'media',
          'completionMedia',
        ],
        order: {
          createdAt: 'DESC',
        },
      });

      const aiQuestsResp = activeAIquestsForToday.map((item) =>
        AIQuestDTO.transform(item),
      );

      const epActiveQuests = await this.questRepo.find({
        where: {
          enterprise: { id: user.enterprise.id },
          scope: QUEST_SCOPE.ENTERPRISE,
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
        },
        relations: ['enterprise', 'questType', 'media', 'participants'],
        order: {
          createdAt: 'DESC',
        },
      });

      const filteredEpQuests = epActiveQuests.filter((quest) => {
        if (!quest.tags || quest.tags.length === 0) return true;
        return user.tags && quest.tags.some((tag) => user.tags.includes(tag));
      });

      const epQuestsResp = await Promise.all(
        filteredEpQuests.map(async (quest) => {
          const participant = await this.participantRepo.findOne({
            where: {
              user: { id: user.id },
              quest: { id: quest.id },
            },
          });

          const isCompleted =
            participant?.status === PARTICIPANT_STATUS.COMPLETED;

          return QuestDTO.transform(quest, true, isCompleted);
        }),
      );

      if (scope) {
        return {
          error: false,
          status: true,
          nbHits:
            scope === QUEST_SCOPE.AI
              ? aiQuestsResp.length
              : epQuestsResp.length,
          quests: scope === QUEST_SCOPE.AI ? aiQuestsResp : epQuestsResp,
        };
      }

      return {
        error: false,
        status: req['status'],
        nbHits: aiQuestsResp.length + epQuestsResp.length,
        quests: [...aiQuestsResp, ...epQuestsResp],
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Error retrieving quests.');
    }
  }

  async getAIQuestById(
    questId: string,
    user: UserEntity,
  ): Promise<getSingleAIQuestByIdResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        assignedToUser: { id: user.id },
        id,
        isDeleted: false,
      },
      relations: ['assignedToUser', 'questType', 'media', 'completionMedia'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found.');
    }

    const resp = AIQuestDTO.transform(quest);

    return {
      error: false,
      quest: resp,
    };
  }

  async submitToAIQuest(
    user: UserEntity,
    AIQuestId: string,
    submission_medias: Express.Multer.File[],
    completionData: submitAIQuestReqDTO,
  ): Promise<SubmitAIQuestResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);
      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['completionMedia', 'assignedToUser', 'questType'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found.');
      }

      if (!quest.isActive) {
        throw new BadRequestException('Quest is not currently active.');
      }

      if (quest.isCompleted) {
        throw new BadRequestException('Quest is already completed by you.');
      }

      await this.checkSubmissionMediaTypes(
        submission_medias,
        quest.submissionMediaType,
      );

      const userCredit = await this.dataSource.transaction(async (manager) => {
        const { caption, completeDate } = completionData;

        this.leaderboardUtilsService.validateDateFormat(completeDate);

        let completionMedias: QuestCompletionProofMediaEntity[];

        if (submission_medias?.length > 0) {
          completionMedias = await Promise.all(
            submission_medias.map(async (item) => {
              let completionMedia = new QuestCompletionProofMediaEntity();

              completionMedia.caption = caption;
              completionMedia.quest = quest;
              completionMedia.userToSubmit = user;

              const uploadedFile = await this.s3Service.uploadFile(item);
              completionMedia.url = uploadedFile.Location;

              const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

              const mediaType = this.determineMediaType(fileExt);

              completionMedia.type = SUBMISSION_MEDIA_TYPES[mediaType];

              return completionMedia;
            }),
          );

          completionMedias = await manager.save(completionMedias);
        } else {
          let completionMedia = new QuestCompletionProofMediaEntity();

          completionMedia.caption = caption;
          completionMedia.quest = quest;
          completionMedia.userToSubmit = user;
          completionMedia.type =
            SUBMISSION_MEDIA_TYPES[quest.submissionMediaType.toUpperCase()];

          completionMedias = [await manager.save(completionMedia)];
        }

        if (completionMedias.length > 0) {
          quest.completionMedia = completionMedias;
        }

        quest.isCompleted = true;

        await manager.save(quest);
        await manager.save(user);

        const userCredit = await this.createUserCredit(
          manager,
          user,
          quest,
          completeDate,
          completionData.caption,
        );

        await this.trackQuestCompletion(manager, user, quest);
        return userCredit;
      });

      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        userCredit.credits,
        userCredit,
      );

      const userCreditResp = UserCreditsDTO.transform(userCredit);

      return {
        error: false,
        msg: 'Quest submitted successfully !!',
        userCredit: userCreditResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async createUserCredit(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
    completeDate: string,
    answer: string | MCQSubmissionDTO,
  ): Promise<UserCreditsEntity> {
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const credits = quest.completionCredits;
      if (credits === null || credits === undefined || isNaN(credits)) {
        console.error('Invalid credits value:', credits);
        newUserCredit.credits = 0;
      } else {
        newUserCredit.credits = Number(credits);
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = quest?.id
        ? ({
            id: quest.id,
          } as QuestEntity)
        : null;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      await this.createUserQuestMetrics(quest, user, answer, manager);

      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting quest,Please try again later....',
      );
    }
  }

  async createUserCreditWithMCQDetails(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
    completeDate: string,
    answer: MCQSubmissionDTO,
    questionsWithAnswers: any[],
    scorePercentage?: number,
  ): Promise<UserCreditsEntity> {
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const baseCredits = quest.completionCredits;

      if (
        baseCredits === null ||
        baseCredits === undefined ||
        isNaN(baseCredits)
      ) {
        console.error('Invalid credits value:', baseCredits);
        newUserCredit.credits = 0;
      } else {
        if (scorePercentage !== undefined && scorePercentage !== null) {
          const calculatedCredits = Math.round(
            (Number(baseCredits) * scorePercentage) / 100,
          );
          newUserCredit.credits = calculatedCredits;
        } else {
          newUserCredit.credits = Number(baseCredits);
        }
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = quest?.id
        ? ({
            id: quest.id,
          } as QuestEntity)
        : null;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      await this.createUserQuestMetrics(
        quest,
        user,
        answer,
        manager,
        newUserCredit.credits,
      );

      // Save and return
      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting quest,Please try again later....',
      );
    }
  }

  async analyzeUserScore(
    quest: QuestEntity,
    answer: string | null,
  ): Promise<{
    analysisScore: number;
    aiSuggestion: string;
    improvementNeeded: string;
  }> {
    if (answer !== null) {
      const { aiSuggestion, analysisScore, improvementNeeded } =
        await this.aWSLlamaAIService.getQuestAnalysis(quest, answer);

      return {
        analysisScore,
        aiSuggestion,
        improvementNeeded,
      };
    } else {
      return {
        analysisScore: 100,
        aiSuggestion:
          'Consider adding more details and examples to improve your answer.',
        improvementNeeded:
          'Focus on providing more specific details and ensuring clarity in your response.',
      };
    }
  }

  async createUserQuestMetrics(
    quest: QuestEntity | any,
    user: UserEntity,
    answer: string | MCQSubmissionDTO,
    manager: EntityManager,
    awardedCredits?: number,
  ) {
    try {
      const userQuestMetrics = new UserQuestMetricsEntity();

      if (quest.isProductHuntQuest) {
        userQuestMetrics.quest = null;
        userQuestMetrics.productQuest = {
          id: quest.id,
        } as ProductQuestEntity;
      } else {
        userQuestMetrics.quest = quest;
        userQuestMetrics.productQuest = null;
      }

      userQuestMetrics.questType = quest.questType;
      userQuestMetrics.user = user;
      userQuestMetrics.enterprise = user.enterprise;

      if (awardedCredits !== undefined && awardedCredits !== null) {
        userQuestMetrics.credits = awardedCredits;
      } else if (
        quest.completionCredits !== undefined &&
        quest.completionCredits !== null
      ) {
        userQuestMetrics.credits = quest.completionCredits;
      } else {
        userQuestMetrics.credits = 0;
        console.warn(
          'No valid credits value found for quest metrics, using 0 as fallback',
        );
      }

      if (quest.submissionMediaType === SUBMISSION_MEDIA_TYPES.MCQ) {
        if (typeof answer !== 'string') {
          const mcqAnswer = answer as MCQSubmissionDTO;

          let correctAnswers = 0;
          let totalQuestions = 0;
          const questionsWithAnswers = [];
          let totalAnalysisScore = 0;

          if (quest.isProductHuntQuest) {
            if (
              quest.questionsWithAnswers &&
              Array.isArray(quest.questionsWithAnswers)
            ) {
              totalQuestions = quest.questionsWithAnswers.length;
              for (const qa of quest.questionsWithAnswers) {
                if (qa.isCorrect) {
                  correctAnswers++;
                }
                questionsWithAnswers.push(qa);
              }
            }
          } else {
            totalQuestions = quest.mcqQuestions ? quest.mcqQuestions.length : 0;

            if (quest.mcqQuestions) {
              for (const userAnswer of mcqAnswer.answers) {
                const question = quest.mcqQuestions.find(
                  (q) => q.id === userAnswer.questionId,
                );
                if (question) {
                  const isCorrect =
                    userAnswer.selectedOptions.length ===
                      question.correctAnswers.length &&
                    userAnswer.selectedOptions.every((opt) =>
                      question.correctAnswers.includes(opt),
                    );
                  if (isCorrect) {
                    correctAnswers++;
                  }

                  questionsWithAnswers.push({
                    question: question.question,
                    options: question.options,
                    correctAnswers: question.correctAnswers,
                    userAnswers: userAnswer.selectedOptions,
                    isCorrect: isCorrect,
                  });
                }
              }
            }
          }

          const { analysisScore, evaluation } =
            await this.aWSLlamaAIService.getMCQQuestAnalysis(
              quest,
              correctAnswers,
              totalQuestions,
              questionsWithAnswers,
            );

          userQuestMetrics.overallAnalysisScore = analysisScore;
          userQuestMetrics.overallAISuggestion = JSON.stringify(evaluation);
          userQuestMetrics.overallImprovementNeeded =
            JSON.stringify(evaluation);

          userQuestMetrics.answer = JSON.stringify(mcqAnswer);
        }
      } else if (quest.submissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT) {
        if (typeof answer === 'string') {
          const { aiSuggestion, analysisScore, improvementNeeded } =
            await this.analyzeUserScore(quest, answer);

          userQuestMetrics.answer = answer;
          userQuestMetrics.overallAnalysisScore = analysisScore;
          userQuestMetrics.overallAISuggestion = aiSuggestion;
          userQuestMetrics.overallImprovementNeeded = improvementNeeded;
        }
      }

      await manager.save(UserQuestMetricsEntity, userQuestMetrics);
    } catch (error) {
      console.error({ error });
      throw error;
    }
  }

  async createUserQuestMetricsWithMCQDetails(
    quest: QuestEntity,
    user: UserEntity,
    answer: MCQSubmissionDTO,
    questionsWithAnswers: any[],
    manager: EntityManager,
  ) {
    try {
      const userQuestMetrics = new UserQuestMetricsEntity();
      userQuestMetrics.quest = quest;
      userQuestMetrics.questType = quest.questType;
      userQuestMetrics.user = user;
      userQuestMetrics.enterprise = user.enterprise;
      userQuestMetrics.credits = quest.completionCredits;

      let correctAnswers = 0;
      const totalQuestions = questionsWithAnswers.length;

      for (const questionData of questionsWithAnswers) {
        if (questionData.isCorrect) {
          correctAnswers++;
        }
      }

      const { analysisScore, evaluation } =
        await this.aWSLlamaAIService.getMCQQuestAnalysis(
          quest,
          correctAnswers,
          totalQuestions,
          questionsWithAnswers, 
        );

      userQuestMetrics.overallAnalysisScore = analysisScore;
      userQuestMetrics.overallAISuggestion = JSON.stringify(evaluation);
      userQuestMetrics.overallImprovementNeeded = JSON.stringify(evaluation);

      userQuestMetrics.answer = JSON.stringify(answer);

      await manager.save(UserQuestMetricsEntity, userQuestMetrics);
    } catch (error) {
      console.error({ error });
      throw error;
    }
  }

  private async trackQuestCompletion(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
  ) {
    try {
      const trackingRepo = manager.getRepository(QuestCompletionTrackingEntity);

      let tracking = await trackingRepo.findOne({
        where: {
          user: { id: user.id },
          questType: { id: quest.questType.id },
        },
        relations: ['user', 'questType'],
      });

      if (!tracking) {
        tracking = new QuestCompletionTrackingEntity();
        tracking.user = user;
        tracking.questType = quest.questType;
        tracking.completedQuestsCount = 1;
        tracking.currentDifficulty = QUEST_DIFFICULTY_TYPES.EASY;
        tracking.easyQuestsCompleted = 15;
        tracking.intermediateQuestsCompleted = 25;
        tracking.hardQuestsCompleted = 35;
        tracking.veryHardQuestsCompleted = 45;
      } else {
        this.incrementDifficultyCount(tracking, quest.difficulty);
        tracking.completedQuestsCount++;
      }

      if (this.shouldUpgradeDifficulty(tracking)) {
        tracking.currentDifficulty = this.getNextDifficulty(
          tracking.currentDifficulty,
        );

        user.difficulty = tracking.currentDifficulty;
        await manager.save(user);
      }

      tracking.lastUpdated = new Date();

      // Save the updated tracking record
      await trackingRepo.save(tracking);
    } catch (error) {
      console.error('Error tracking quest completion:', error);
      throw new Error(`Failed to track quest completion: ${error.message}`);
    }
  }

  private incrementDifficultyCount(
    tracking: QuestCompletionTrackingEntity,
    difficulty: QUEST_DIFFICULTY_TYPES,
  ) {
    switch (difficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        tracking.easyQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        tracking.intermediateQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.HARD:
        tracking.hardQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        tracking.veryHardQuestsCompleted++;
        break;
    }
  }

  private shouldUpgradeDifficulty(
    tracking: QuestCompletionTrackingEntity,
  ): boolean {
    switch (tracking.currentDifficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        return tracking.easyQuestsCompleted >= 15;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        return tracking.intermediateQuestsCompleted >= 25;
      case QUEST_DIFFICULTY_TYPES.HARD:
        return tracking.hardQuestsCompleted >= 35;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        return false; 
      default:
        return false;
    }
  }

  private getNextDifficulty(
    currentDifficulty: QUEST_DIFFICULTY_TYPES,
  ): QUEST_DIFFICULTY_TYPES {
    const difficulties = [
      QUEST_DIFFICULTY_TYPES.EASY,
      QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      QUEST_DIFFICULTY_TYPES.HARD,
      QUEST_DIFFICULTY_TYPES.VERY_HARD,
    ];

    const currentIndex = difficulties.indexOf(currentDifficulty);
    return currentIndex < difficulties.length - 1
      ? difficulties[currentIndex + 1]
      : currentDifficulty;
  }

  async getQuestSubmissionMedia(
    user: UserEntity,
    AIQuestId: string,
  ): Promise<GetAIQuestSubmissionMediaResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);

      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['assignedToUser'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found');
      }

      const completionMedias = await this.questCompletionMediaRepo.find({
        where: { userToSubmit: { id: user.id }, quest: { id: quest.id } },
      });

      if (!completionMedias || completionMedias.length <= 0) {
        return { error: false, caption: '', media_urls: [], metrics: {} };
      }

      const questMetric = await this.userQuestMetricsRepo.findOne({
        where: {
          quest: { id: parseInt(AIQuestId, 10) },
          user: { id: user.id },
        },
      });

      return {
        error: false,
        caption: completionMedias[0].caption,
        media_urls:
          quest.submissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT
            ? completionMedias.map((item) => item.url)
            : [],
        metrics: questMetric
          ? {
              id: questMetric.id,
              overallAnalysisScore: questMetric.overallAnalysisScore,
              credits: questMetric.credits,
              createdAt: questMetric.createdAt,
              evaluation: this.parseEvaluationForResponse(
                questMetric,
                quest.submissionMediaType,
              ),
            }
          : {},
      };
    } catch (error) {
      return { error: false, caption: '', media_urls: [], metrics: {} };
    }
  }

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }

  private async checkSubmissionMediaTypes(
    submission_medias: Express.Multer.File[],
    questSubmissionMediaType: string,
  ) {
    if (
      questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length > 0
    ) {
      throw new BadRequestException(
        'Provide only text for this quest submission.',
      );
    }

    if (
      questSubmissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length <= 0
    ) {
      throw new BadRequestException(
        'Please provided submission media for quest submission.',
      );
    }

    if (questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.MIXED) {
      const allMediaTypesMatch = submission_medias.map((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        const mediaType = this.determineMediaType(fileExt);

        return mediaType;
      });

      if (allMediaTypesMatch.length !== 1) {
        throw new BadRequestException(
          `All submission media type must be of same type`,
        );
      }
    } else {
      const allMediaTypesMatch = submission_medias.every((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';
        const mediaType = this.determineMediaType(fileExt);
        return mediaType === questSubmissionMediaType;
      });

      if (!allMediaTypesMatch) {
        throw new BadRequestException(
          `All submission media type must be of type ${questSubmissionMediaType}`,
        );
      }
    }
  }

  private determineMediaType(fileExt: string): string {
    if (AllowedVideoExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.VIDEO;
    } else if (AllowedImageExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.IMAGE;
    }
    return SUBMISSION_MEDIA_TYPES.TEXT;
  }

  async createMCQQuest(
    createDto: CreateMCQQuestDTO,
    user: UserEntity,
  ): Promise<QuestEntity> {
    if (!user.enterprise) {
      throw new BadRequestException(
        'Only enterprise users can create MCQ quests',
      );
    }

    let endDate: Date;
    if (createDto.endDate) {
      endDate = new Date(createDto.endDate);
      if (isNaN(endDate.getTime())) {
        throw new BadRequestException('Invalid end date provided');
      }
      // Validate that end date is in the future
      if (endDate <= new Date()) {
        throw new BadRequestException('End date must be in the future');
      }
    } else {
      endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); 
    }

    const quest = this.questRepo.create({
      title: createDto.title,
      description: createDto.description,
      difficulty: createDto.difficulty,
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE, 
      isActive: true,
      startDate: new Date(),
      endDate: endDate,
      completionCredits: createDto.completionCredits || 100,
      enterprise: user.enterprise,
      creator: user,
      tags: createDto.tags || [], 
    });

    return await this.questRepo.save(quest);
  }

  async submitMCQAnswers(
    questId: number,
    submission: MCQSubmissionDTO,
    user: UserEntity,
  ) {
    const quest = await this.questRepo.findOne({
      where: { id: questId },
      relations: ['mcqQuestions', 'assignedToUser', 'enterprise', 'questType'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.scope !== QUEST_SCOPE.AI) {
      throw new BadRequestException(
        'This endpoint only handles AI-scope MCQ quests. Use enterprise quest endpoints for enterprise MCQs.',
      );
    }

    if (!quest.assignedToUser || quest.assignedToUser.id !== user.id) {
      throw new BadRequestException(
        'You are not authorized to submit answers for this quest',
      );
    }

    let correctAnswers = 0;
    let totalQuestions = quest.mcqQuestions.length;

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      if (!question) {
        throw new BadRequestException(
          `Question with ID ${answer.questionId} not found`,
        );
      }

      const invalidOptions = answer.selectedOptions.filter(
        (opt) => opt < 0 || opt >= question.options.length,
      );
      if (invalidOptions.length > 0) {
        throw new BadRequestException(
          `Invalid option indices: ${invalidOptions.join(', ')}`,
        );
      }

      const isCorrect =
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every((opt) =>
          question.correctAnswers.includes(opt),
        );

      if (isCorrect) {
        correctAnswers++;
      }
    }

    const score = (correctAnswers / totalQuestions) * 100;

    // Mark AI quest as completed
    quest.isCompleted = true;
    await this.questRepo.save(quest);

    const questionsWithAnswers = [];

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      const isCorrect =
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every((opt) =>
          question.correctAnswers.includes(opt),
        );

      questionsWithAnswers.push({
        question: question.question,
        options: question.options,
        correctAnswers: question.correctAnswers,
        userAnswers: answer.selectedOptions,
        isCorrect: isCorrect,
      });
    }

    const userCredit = await this.createUserCreditWithMCQDetails(
      this.dataSource.manager,
      user,
      quest,
      new Date().toISOString(),
      { answers: submission.answers } as MCQSubmissionDTO,
      questionsWithAnswers,
      score,
    );

    try {
      const existingMetrics = await this.userQuestMetricsRepo.findOne({
        where: {
          user: { id: user.id },
          quest: { id: questId },
        },
      });

      if (existingMetrics) {
        existingMetrics.answer = JSON.stringify(submission);
        await this.userQuestMetricsRepo.save(existingMetrics);
      }
    } catch (error) {}

    if (userCredit.credits > 0) {
      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        userCredit.credits,
        userCredit,
      );
    }

    return {
      score,
      correctAnswers,
      totalQuestions,
      passed: score >= 70,
      userCredit: UserCreditsDTO.transform(userCredit),
    };
  }

  async getMCQQuestions(questId: number, user: UserEntity) {
    const quest = await this.questRepo.findOne({
      where: {
        id: questId,
        enterprise: { id: user.enterprise.id },
        submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      },
      relations: ['mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('MCQ quest not found');
    }

    const isHR = user.roles?.some((role) => role.value === ROLE_VALUES.HR);

    if (!isHR) {
      if (quest.tags && quest.tags.length > 0) {
        if (!user.tags || !quest.tags.some((tag) => user.tags.includes(tag))) {
          throw new BadRequestException(
            'You are not authorized to access this quest',
          );
        }
      }
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: questId },
        status: PARTICIPANT_STATUS.COMPLETED,
      },
    });

    if (participant) {
      throw new BadRequestException('You have already completed this quest');
    }

    return {
      questions: quest.mcqQuestions.map((q) => ({
        id: q.id,
        question: q.question,
        options: q.options,
        difficulty: q.difficulty,
        ...(isHR && { correctAnswers: q.correctAnswers }),
      })),
    };
  }

  async acceptMCQQuestions(
    user: UserEntity,
    mcqs: any[],
    questId: string,
  ): Promise<boolean> {
    if (!mcqs || !Array.isArray(mcqs) || mcqs.length === 0) {
      throw new BadRequestException('No MCQ questions provided');
    }

    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException(
        'You do not have permission to modify this quest',
      );
    }

    try {
      const mcqEntities = mcqs.map((mcq) => {
        return this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options || [],
          correctAnswers: mcq.correctAnswers || [0],
          difficulty: mcq.difficulty || 'intermediate',
          quest,
        });
      });

      await this.mcqQuestionRepo.save(mcqEntities);
      this.logger.log(
        `Saved ${mcqEntities.length} MCQ questions for quest ID ${questId}`,
      );

      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to accept MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/accept`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to save MCQ questions');
    }
  }

  async rejectMCQQuestions(
    user: UserEntity,
    questId: string,
  ): Promise<boolean> {
    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator', 'mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException(
        'You do not have permission to modify this quest',
      );
    }

    try {
      if (quest.mcqQuestions && quest.mcqQuestions.length > 0) {
        await this.mcqQuestionRepo.remove(quest.mcqQuestions);
      }

      this.logger.log(
        `Rejected and removed MCQ questions for quest ID ${questId}`,
      );
      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to reject MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/reject`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to reject MCQ questions');
    }
  }

  private parseEvaluationForResponse(
    questMetric: UserQuestMetricsEntity,
    submissionMediaType?: string,
  ): any {
    if (submissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT) {
      return questMetric.overallImprovementNeeded || '';
    }

    try {
      const parsed = JSON.parse(questMetric.overallAISuggestion);
      if (parsed && typeof parsed === 'object') {
        // Return the enhanced evaluation structure
        return {
          strength: parsed.strength || '',
          weakness: parsed.weakness || '',
          recommendation: parsed.recommendation || '',
        };
      }
    } catch (error) {
    }

    return questMetric.overallAISuggestion || '';
  }

  async getAllProductQuests(user: UserEntity) {
    try {
      const startOfToday = new Date();
      startOfToday.setHours(0, 0, 0, 0);
      const endOfToday = new Date();
      endOfToday.setHours(23, 59, 59, 999);

      const allProductQuests = await this.productQuestRepo.find({
        where: {
          enterpriseId: user.enterprise.id,
          assignToUserId: user.id,
          isDeleted: false,
          isActive: true,
        },
        order: { createdAt: 'DESC' },
        relations: ['productSummary', 'questType'],
      });

      const todayQuests = [];
      const otherQuests = [];

      for (const quest of allProductQuests) {
        if (quest.createdAt >= startOfToday && quest.createdAt <= endOfToday) {
          todayQuests.push(quest);
        } else {
          const submission = await this.productQuestSubmissionRepo.findOne({
            where: {
              productQuest: { id: quest.id },
              submittedByUserId: { id: user.id },
            },
          });
          if (submission) {
            otherQuests.push(quest);
          }
        }
      }

      const quests = await Promise.all(
        [...todayQuests, ...otherQuests].map(async (quest) => {
          let questionAnswer = null;
          let mcqQuestions = [];

          if (quest.submissionType === 'TEXT') {
            questionAnswer = await this.dataSource
              .getRepository('ProductQuestionAnswerEntity')
              .findOne({ where: { productQuestId: quest.id } });
          }

          if (quest.submissionType === 'MCQ') {
            mcqQuestions = await this.productMCQQuestRepo.find({
              where: { questId: quest.id, isDeleted: false },
              order: { id: 'ASC' },
            });
          }

          const productQuestSubmissions =
            await this.productQuestSubmissionRepo.findOne({
              where: {
                productQuest: { id: quest.id },
                submittedByUserId: { id: user.id },
              },
            });

          return {
            id: quest.id,
            title: quest.title,
            description: quest.description,
            difficulty: quest.difficulty,
            submissionType: quest.submissionType,
            completionCredits: quest.completionCredits,
            startDate: quest.startDate,
            endDate: quest.endDate,
            scope: 'enterprise',
            isActive:
              quest.endDate &&
              new Date(quest.endDate).getTime() >=
                new Date(Date.now()).getTime()
                ? true
                : false,
            isCompleted: productQuestSubmissions ? true : false,
            productSummaryId: quest.productSummaryId,
            enterpriseId: quest.enterpriseId,
            userId: quest.assignToUserId,
            tags: quest.tags,
            createdAt: quest.createdAt,
            updatedAt: quest.updatedAt,
            questType: quest.questType,
            productSummary: quest.productSummary,
            ...(questionAnswer && { questionAnswer }),
            ...(mcqQuestions.length > 0 && { mcqQuestions }),
          };
        }),
      );

      return {
        error: false,
        status: true,
        nbHits: quests.length,
        quests,
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Error retrieving product quests.');
    }
  }

  async getProductHuntQuestById(user: UserEntity, productHuntQuestId: string) {
    const id = parseInt(productHuntQuestId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid product hunt quest ID');
    }

    const quest = await this.productQuestRepo.findOne({
      where: {
        id,
        enterpriseId: user.enterprise.id,
        isDeleted: false,
      },
      relations: ['user', 'questType'],
    });

    if (!quest) {
      throw new BadRequestException(
        'Product hunt quest not found or not authorized',
      );
    }

    let questionAnswer = null;
    let mcqs = null;

    if (quest.submissionType === 'TEXT') {
      questionAnswer = await this.dataSource
        .getRepository('ProductQuestionAnswerEntity')
        .findOne({
          where: { productQuestId: quest.id },
          select: [
            'id',
            'question',
            'productQuestId',
            'createdAt',
            'updatedAt',
          ],
        });
    } else if (quest.submissionType === 'MCQ') {
      mcqs = await this.productMCQQuestRepo.find({
        where: { questId: quest.id, isDeleted: false },
        select: [
          'id',
          'question',
          'options',
          'difficulty',
          'questId',
          'createdAt',
          'updatedAt',
        ],
        order: { id: 'ASC' },
      });
    }

    return {
      error: false,
      quest: {
        ...quest,
        createdBy: 'AI',
      },
      ...(questionAnswer && { questionAnswer }),
      ...(mcqs && { mcqs }),
    };
  }

  async submitProductHuntQuestSubmission(
    user: UserEntity,
    productHuntQuestId: string,
    submission: {
      answer?: string;
      mcqAnswers?: { questionId: number; selectedOptions: number[] }[];
    },
  ) {
    const questId = parseInt(productHuntQuestId, 10);
    if (isNaN(questId)) {
      throw new BadRequestException('Invalid product hunt quest ID');
    }

    const quest: ProductQuestEntity | null =
      await this.productQuestRepo.findOne({
        where: { id: questId, isDeleted: false },
      });
    if (!quest) {
      throw new NotFoundException('Product hunt quest not found');
    }

    if (
      !user.enterprise ||
      !user.enterprise.id ||
      quest.enterpriseId !== user.enterprise.id
    ) {
      throw new BadRequestException('User does not belong to any enterprise');
    }

    // Check if the quest is expired
    const now = new Date();
    if (quest.endDate < now) {
      throw new BadRequestException('Quest is expired');
    }

    const existing = await this.productQuestSubmissionRepo.findOne({
      where: {
        productQuest: { id: questId },
        submittedByUserId: { id: user.id },
      },
    });
    if (existing) {
      throw new BadRequestException('You have already submitted this quest.');
    }

    let submissionType: PRODUCT_QUEST_SUBMISSION_TYPE;
    let answer: string | null = null;
    let mcqAnswers: { questionId: number; selectedOptions: number[] }[] | null =
      null;

    if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.TEXT) {
      if (!submission.answer || typeof submission.answer !== 'string') {
        throw new BadRequestException('Answer is required for Q&A quest.');
      }
      submissionType = PRODUCT_QUEST_SUBMISSION_TYPE.TEXT;
      answer = submission.answer;
    } else if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.MCQ) {
      if (
        !Array.isArray(submission.mcqAnswers) ||
        submission.mcqAnswers.length === 0
      ) {
        throw new BadRequestException(
          'MCQ answers are required for MCQ quest.',
        );
      }
      submissionType = PRODUCT_QUEST_SUBMISSION_TYPE.MCQ;
      mcqAnswers = submission.mcqAnswers;
    } else {
      throw new BadRequestException('Invalid quest submission type.');
    }

    let aiAnalysisResults = null;
    if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.TEXT) {
      try {
        let fullProductSummary = '';
        if (quest.productSummary && quest.productSummary.summary) {
          fullProductSummary = quest.productSummary.summary;
        }

        const questForAIWithContext = {
          ...quest, 
          productSummaryContext: fullProductSummary, 
          isProductHuntQuest: true, 
        };

        const { aiSuggestion, analysisScore, improvementNeeded } =
          await this.analyzeUserScore(questForAIWithContext as any, answer);
        const roundedScore = Math.round(analysisScore / 5) * 5;

        aiAnalysisResults = {
          aiSuggestion,
          analysisScore,
          improvementNeeded,
          roundedScore,
        };
      } catch (aiError) {
        console.error('AI analysis failed, using defaults:', aiError);
        aiAnalysisResults = {
          aiSuggestion: 'Unable to analyze at this time.',
          analysisScore: 50,
          improvementNeeded:
            'Please review your answer for clarity and completeness.',
          roundedScore: 50,
        };
      }
    }

    const submissionEntity = this.productQuestSubmissionRepo.create({
      productQuest: quest,
      submittedByUserId: user,
      submissionType,
      answer,
      mcqAnswers,
    });
    await this.productQuestSubmissionRepo.save(submissionEntity);

 
    let userCredit: UserCreditsEntity | null = null;
    let score = 0;
    let correctAnswers = 0;
    let totalQuestions = 0;

    await this.dataSource.transaction(async (manager) => {
      let questType = quest.questType;
      if (!questType) {
        questType = await manager
          .getRepository(QuestTypesEntity)
          .findOne({ where: { value: 'PRODUCT_QUEST' } });
        if (!questType) throw new Error('QuestType PRODUCT_QUEST not found');
        quest.questType = questType;
      }
      const userQuestMetrics = this.userQuestMetricsRepo.create();
      userQuestMetrics.productQuest = quest;
      userQuestMetrics.questType = questType as QuestTypesEntity;
      userQuestMetrics.user = user;
      userQuestMetrics.enterprise = user.enterprise;
      userQuestMetrics.credits = quest.completionCredits;

      const questForAI = {
        title: quest.title,
        description: quest.description,
        questType: questType,
        completionCredits: quest.completionCredits,
      };

      if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.MCQ) {
        // Fetch MCQ questions for this quest
        const mcqQuestions = await this.productMCQQuestRepo.find({
          where: { questId: quest.id, isDeleted: false },
          order: { id: 'ASC' },
        });

        correctAnswers = 0;
        totalQuestions = mcqQuestions.length;
        const questionsWithAnswers = [];

        for (const userAnswer of mcqAnswers || []) {
          const question = mcqQuestions.find(
            (q) => q.id === userAnswer.questionId,
          );

          if (!question) {
            throw new BadRequestException(
              `Question with ID ${userAnswer.questionId} not found`,
            );
          }

          const invalidOptions = userAnswer.selectedOptions.filter(
            (opt) => opt < 0 || opt >= question.options.length,
          );
          if (invalidOptions.length > 0) {
            throw new BadRequestException(
              `Invalid option indices: ${invalidOptions.join(', ')}`,
            );
          }

          const isCorrect =
            userAnswer.selectedOptions.length ===
              question.correctAnswers.length &&
            userAnswer.selectedOptions.every((opt) =>
              question.correctAnswers.includes(opt),
            );

          if (isCorrect) {
            correctAnswers++;
          }

          questionsWithAnswers.push({
            question: question.question,
            options: question.options,
            correctAnswers: question.correctAnswers,
            userAnswers: userAnswer.selectedOptions,
            isCorrect,
          });
        }

        score =
          totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

        userQuestMetrics.answer = JSON.stringify(mcqAnswers);
        userQuestMetrics.overallAnalysisScore = Math.round(score); 
        userQuestMetrics.overallAISuggestion = `Score: ${correctAnswers}/${totalQuestions} (${Math.round(score)}%)`;
        userQuestMetrics.overallImprovementNeeded =
          score >= 70
            ? 'Good performance'
            : 'Review incorrect answers and study the material again';

        userCredit = await this.createUserCreditForProductHuntMCQ(
          manager,
          user,
          quest,
          new Date().toISOString(),
          { answers: mcqAnswers } as MCQSubmissionDTO,
          questionsWithAnswers,
          score,
        );
      } else if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.TEXT) {
        userCredit = await this.createUserCreditForProductHuntText(
          manager,
          user,
          quest,
          new Date().toISOString(),
          answer,
          aiAnalysisResults?.roundedScore || 50,
        );

        await this.createUserQuestMetrics(
          {
            id: quest.id,
            title: quest.title,
            description: quest.description,
            completionCredits: quest.completionCredits,
            questType: quest.questType,
            submissionMediaType: SUBMISSION_MEDIA_TYPES.TEXT,
            isProductHuntQuest: true, 
            productSummaryContext: quest.productSummary?.summary || '', 
          } as any,
          user,
          answer,
          manager,
          userCredit.credits,
        );
      }

      await manager.save(UserQuestMetricsEntity, userQuestMetrics);

      if (quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.MCQ) {
        if (userCredit && userCredit.credits > 0) {
          await this.leaderboardService.updateLeaderboards(
            user,
            questType,
            userCredit.credits,
            userCredit,
          );
        }
      } else {
        if (userCredit && userCredit.credits > 0) {
          await this.leaderboardService.updateLeaderboards(
            user,
            questType,
            userCredit.credits,
            userCredit,
          );
        }
      }
    });

    return {
      error: false,
      message: 'Product Hunt quest submitted successfully.',
      creditsEarned: userCredit?.credits || 0,
      ...(quest.submissionType === PRODUCT_QUEST_SUBMISSION_TYPE.MCQ && {
        score,
        correctAnswers,
        totalQuestions,
        passed: score >= 70, 
      }),
    };
  }

  async createUserCreditForProductHuntMCQ(
    manager: EntityManager,
    user: UserEntity,
    quest: ProductQuestEntity,
    completeDate: string,
    answer: MCQSubmissionDTO,
    questionsWithAnswers: any[],
    scorePercentage?: number,
  ): Promise<UserCreditsEntity> {
    // Validate inputs
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const baseCredits = quest.completionCredits;

      if (
        baseCredits === null ||
        baseCredits === undefined ||
        isNaN(baseCredits)
      ) {
        console.error('Invalid credits value:', baseCredits);
        newUserCredit.credits = 0;
      } else {
        if (scorePercentage !== undefined && scorePercentage !== null) {
          const calculatedCredits = Math.round(
            (Number(baseCredits) * scorePercentage) / 100,
          );
          newUserCredit.credits = calculatedCredits;
        } else {
          newUserCredit.credits = Number(baseCredits);
        }
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = null;
      
      newUserCredit.productQuest = {
        id: quest.id,
      } as ProductQuestEntity;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      await this.createUserQuestMetrics(
        {
          id: quest.id,
          title: quest.title,
          description: quest.description,
          completionCredits: quest.completionCredits,
          questType: quest.questType,
          submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
          isProductHuntQuest: true, 
          questionsWithAnswers: questionsWithAnswers, 
        } as any,
        user,
        answer,
        manager,
        newUserCredit.credits,
      );

      // Save and return
      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting Product Hunt quest, Please try again later....',
      );
    }
  }

  async createUserCreditForProductHuntText(
    manager: EntityManager,
    user: UserEntity,
    quest: ProductQuestEntity,
    completeDate: string,
    answer: string,
    analysisScore: number,
  ): Promise<UserCreditsEntity> {
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const baseCredits = quest.completionCredits;

      if (
        baseCredits === null ||
        baseCredits === undefined ||
        isNaN(baseCredits)
      ) {
        console.error('Invalid credits value:', baseCredits);
        newUserCredit.credits = 0;
      } else {
        const calculatedCredits = Math.round(
          (analysisScore / 100) * baseCredits,
        );
        newUserCredit.credits = calculatedCredits;
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = null;
      
      newUserCredit.productQuest = {
        id: quest.id,
      } as ProductQuestEntity;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

     

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting Product Hunt TEXT quest, Please try again later....',
      );
    }
  }

  async getProductHuntSubmissionQuestById(
    user: UserEntity,
    productHuntQuestId: string,
  ) {
    const questId = parseInt(productHuntQuestId, 10);
    if (isNaN(questId)) {
      throw new BadRequestException('Invalid product hunt quest ID');
    }

    try {
      const quest = await this.productQuestRepo.findOne({
        where: {
          id: questId,
          enterpriseId: user.enterprise.id,
          isDeleted: false,
        },
        relations: ['user', 'questType', 'productSummary'],
      });

      if (!quest) {
        throw new BadRequestException(
          'Product hunt quest not found or not authorized',
        );
      }

      const submission = await this.productQuestSubmissionRepo.findOne({
        where: {
          productQuest: { id: questId },
          submittedByUserId: { id: user.id },
        },
        relations: ['productQuest', 'submittedByUserId'],
      });

      const metrics = await this.userQuestMetricsRepo.findOne({
        where: {
          productQuest: { id: questId },
          user: { id: user.id },
        },
      });

      return {
        error: false,
        quest: {
          ...quest,
          createdBy: 'AI',
        },
        submission: submission || null,
        metrics: metrics
          ? {
              id: metrics.id,
              overallAnalysisScore: metrics.overallAnalysisScore,
              credits: metrics.credits,
              createdAt: metrics.createdAt,
              evaluation: this.parseEvaluationForResponse(
                metrics,
                quest.submissionType,
              ),
            }
          : null,
        isCompleted: !!submission,
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Failed to fetch quest submission details');
    }
  }
}
