import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  CreateAIQuestResDTO,
  GetAIQuestSubmissionMediaResDTO,
  SubmitAIQuestResDTO,
  getSingleAIQuestByIdResDTO,
  submitAIQuestReqDTO,
  SubmitProductHuntQuestResponseDto,
} from './AI-quest-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { AIQuestService } from './AI-quest.service';
import { Request } from 'express';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import { AllowedMixEntensions } from 'src/utils/allowedExtensions.utils';
import { GetAllUserQuestsQueryFilterInterface } from './interfaces';
import { QuestEntity } from '../../models/quest-entity';
import { MCQGenerationService } from './mcq-generation.service';
import { CreateMCQQuestDTO, MCQSubmissionDTO } from './AI-quest-dto/AI-mcq.dto';
import { CustomLogger } from 'src/common/logger/custom-logger.service';

@ApiTags('user-quests')
@ApiBearerAuth()
@Controller()
export class AIQuestController {
  constructor(
    private readonly AIQuestService: AIQuestService,
    private readonly userProfileService: UserProfileService,
    private readonly mcqService: MCQGenerationService,
    private readonly logger: CustomLogger,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get All User Quests',
    type: CreateAIQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/quests') // middleware will run on this route
  @UseGuards(AuthGuard)
  async GetAllAIQuests(
    @Req() req: Request,
    @Query() filterData: GetAllUserQuestsQueryFilterInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.getAllUserQuests(user, req, filterData);
  }

  @ApiResponse({
    status: 200,
    description: 'Submit to AI quest',
    type: SubmitAIQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('ai/quests/submit/:questId')
  @UseInterceptors(
    FilesInterceptor(
      'submission_media',
      3,
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  @UseGuards(AuthGuard)
  async SubmitAIQuest(
    @Req() req: Request,
    @Param('questId') questId: string,
    @UploadedFiles() submission_medias: Express.Multer.File[],
    @Body() completionData: submitAIQuestReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.submitToAIQuest(
      user,
      questId,
      submission_medias,
      completionData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Get AI Quest Submission Medias',
    type: GetAIQuestSubmissionMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('ai/quests/media/:questId')
  @UseGuards(AuthGuard)
  async GetQuestSubmissionMedia(
    @Req() req: Request,
    @Param('questId') questId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.getQuestSubmissionMedia(user, questId);
  }

  @ApiResponse({
    status: 200,
    description: 'Get AI Quest by ID',
    type: getSingleAIQuestByIdResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('ai/quests/:questId')
  @UseGuards(AuthGuard)
  async GetAIQuestById(@Req() req: Request, @Param('questId') questId: string) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.getAIQuestById(questId, user);
  }

  @Post('ai/quests/mcq/create')
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 201,
    description: 'MCQ Quest created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    type: ErrorResponse,
  })
  async createMCQQuest(
    @Req() req: Request,
    @Body() createDto: CreateMCQQuestDTO,
  ) {
    try {
      const user = await this.userProfileService.getUserFromToken(req);

      // First create the quest
      const quest = await this.AIQuestService.createMCQQuest(createDto, user);

      // Then generate and save MCQ questions from the provided content
      const questions = await this.mcqService.generateQuestionsFromText(
        createDto.content,
        quest,
        createDto.difficulty,
      );

      this.logger.log(
        `Successfully created MCQ quest "${quest.title}" with ${questions.length} questions for enterprise ${user.enterprise?.id}`,
      );

      return {
        questId: quest.id,
        questionCount: questions.length,
        success: true,
        scope: quest.scope,
        enterprise: user.enterprise?.id,
      };
    } catch (error) {
      if (
        error.message.includes('AI service') ||
        error.message.includes('generate MCQs')
      ) {
        throw new BadRequestException({
          error: true,
          message:
            'Failed to generate MCQ questions. Please try with different content or try again later.',
          details: error.message,
        });
      }

      throw error;
    }
  }

  @Post('ai/quests/mcq/:questId/submit')
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'MCQ answers submitted successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  async submitMCQAnswers(
    @Req() req: Request,
    @Param('questId', ParseIntPipe) questId: number,
    @Body() submission: MCQSubmissionDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return await this.AIQuestService.submitMCQAnswers(
      questId,
      submission,
      user,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'MCQ questions retrieved successfully',
    schema: {
      properties: {
        questions: {
          type: 'array',
          items: {
            properties: {
              id: { type: 'number' },
              question: { type: 'string' },
              options: {
                type: 'array',
                items: { type: 'string' },
                description: 'Content-specific options for the question',
              },
              difficulty: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Quest not found',
    type: ErrorResponse,
  })
  @Get('ai/quests/mcq/:questId/questions')
  @UseGuards(AuthGuard)
  async getMCQQuestions(
    @Req() req: Request,
    @Param('questId', ParseIntPipe) questId: number,
  ) {
    try {
      const user = await this.userProfileService.getUserFromToken(req);
      const result = await this.AIQuestService.getMCQQuestions(questId, user);

      return result;
    } catch (error) {
      throw error;
    }
  }

  @ApiResponse({
    status: 200,
    description: 'Accept generated MCQ questions',
    schema: {
      properties: {
        error: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'MCQ questions accepted successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Post('ai/quests/mcq/accept')
  @UseGuards(AuthGuard)
  async acceptMCQQuestions(
    @Req() req: Request,
    @Body() data: { mcqs: any[]; questId: string },
  ) {
    try {
      const user = await this.userProfileService.getUserFromToken(req);
      const result = await this.AIQuestService.acceptMCQQuestions(
        user,
        data.mcqs,
        data.questId,
      );

      return {
        error: false,
        message: 'MCQ questions accepted successfully',
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException(
        error.message || 'Failed to accept MCQ questions',
      );
    }
  }

  @ApiResponse({
    status: 200,
    description: 'Reject generated MCQ questions',
    schema: {
      properties: {
        error: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'MCQ questions rejected successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Post('ai/quests/mcq/reject')
  @UseGuards(AuthGuard)
  async rejectMCQQuestions(
    @Req() req: Request,
    @Body() data: { questId: string },
  ) {
    try {
      const user = await this.userProfileService.getUserFromToken(req);
      await this.AIQuestService.rejectMCQQuestions(user, data.questId);

      return {
        error: false,
        message: 'MCQ questions rejected successfully',
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException(
        error.message || 'Failed to reject MCQ questions',
      );
    }
  }

  @ApiResponse({
    status: 200,
    description: 'Get All User Quests',
    type: CreateAIQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/quests/ai/product-hunt')
  @UseGuards(AuthGuard)
  async GetAllProductQuestsByAi(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);

    const productHuntQuests =
      await this.AIQuestService.getAllProductQuests(user);

    return productHuntQuests;
  }

  @ApiResponse({
    status: 200,
    description: 'Get Product Hunt Quest Details by ID',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/quests/ai/product-hunt/:productHuntQuestId')
  @UseGuards(AuthGuard)
  async getProductHuntQuestById(
    @Req() req: Request,
    @Param('productHuntQuestId') productHuntQuestId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.getProductHuntQuestById(
      user,
      productHuntQuestId,
    );
  }

  @Get('/quests/ai/product-hunt/submission/:productHuntQuestId')
  @UseGuards(AuthGuard)
  async getProductHuntSubmissionQuestById(
    @Req() req: Request,
    @Param('productHuntQuestId') productHuntQuestId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.AIQuestService.getProductHuntSubmissionQuestById(
      user,
      productHuntQuestId,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Submit Product Hunt Quest',
    type: SubmitProductHuntQuestResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('/quests/ai/product-hunt/:productHuntQuestId/submit')
  @UseGuards(AuthGuard)
  async submitProductHuntQuestSubmission(
    @Req() req: Request,
    @Param('productHuntQuestId') productHuntQuestId: string,
    @Body() submission: any, // { answer: string } or { mcqAnswers: [...] }
  ): Promise<SubmitProductHuntQuestResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return await this.AIQuestService.submitProductHuntQuestSubmission(
      user,
      productHuntQuestId,
      submission,
    );
  }
}
