import { Injectable } from '@nestjs/common';
import { CustomLogger } from './logger/custom-logger.service';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { sendTextMailInterface } from './interfaces';

@Injectable()
export class EmailService {
  constructor(
    private readonly sesService: SesService,
    private readonly logger: CustomLogger,
  ) {}

  async sendTextMail({
    toEmail,
    fromEmail,
    subject,
    textBody,
    html,
  }: sendTextMailInterface) {
    try {
      await this.sesService.sendEmail(
        toEmail,
        fromEmail,
        subject,
        textBody,
        html,
      );
    } catch (error) {
      throw error;
    }
  }

  async sendBatchEmail(data: sendTextMailInterface[]) {
    const results = await Promise.allSettled(
      data.map((item) => this.sendTextMail(item)),
    );

    return results;
  }

  async sendBatchEmailsWithDelay(emailsToSend: any[]) {
    const batchSize = Number(process.env.BULK_EMAIL_BATCH_SIZE) || 50;
    const delay = Number(process.env.BULK_EMAIL_BATCH_DELAY) || 1000;

    const totalBatches = Math.ceil(emailsToSend.length / batchSize);

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * batchSize;
      const end = Math.min((batchIndex + 1) * batchSize, emailsToSend.length);

      const currentBatch = emailsToSend.slice(start, end);

      await this.sendBatchEmail(currentBatch);

      if (batchIndex < totalBatches - 1) {
        await this.delay(delay); // Custom delay function
      }
    }
  }

  // Utility function to delay execution
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
