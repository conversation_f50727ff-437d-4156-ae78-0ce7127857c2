import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { DataSource, EntityManager, Like, MoreThan, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  QUEST_MEDIA_TYPE,
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  QuestEntity,
  QuestMediaEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { FeedEntity } from 'src/models/feed-entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import {
  CreateQuestReqDTO,
  CreateQuestResDTO,
  DeleteQuestResDTO,
  QuestDTO,
} from 'src/quest/quest-dto';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { HrUtilsService } from '../hr-utils.service';
import { GetAllQuestsHrResDTO } from './hr-quests-dto';
import { getAllQuestsHRfilterQueriesInterface } from './interfaces';
import { AcceptMCQDto } from './dto/hr-mcq.dto';
import {
  MCQGenerationService,
  MCQQuestion,
} from 'src/quest/AI-quest/mcq-generation.service';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import {
  ProductQuestEntity,
  PRODUCT_QUEST_SUBMISSION_TYPE,
} from 'src/models/products-summary-entity';
import { ProductSummaryEntity } from 'src/models/pdf-parsing-entity';
import { ProductMCQQuestEntity } from 'src/models/products-summary-entity/product-mcq-quest.entity';

@Injectable()
export class HrQuestsService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly s3Service: S3Service,
    private readonly logger: CustomLogger,
    private readonly hrUtilsService: HrUtilsService,
    private readonly mcqGenerationService: MCQGenerationService,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestMediaEntity)
    private readonly questMediaRepo: Repository<QuestMediaEntity>,
    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,

    @InjectRepository(ProductSummaryEntity)
    private readonly productSummaryRepo: Repository<ProductSummaryEntity>,

    @InjectRepository(ProductQuestEntity)
    private readonly productQuestRepo: Repository<ProductQuestEntity>,

    @InjectRepository(ProductMCQQuestEntity)
    private readonly productMCQQuestRepo: Repository<ProductMCQQuestEntity>,

    private readonly aWSLlamaAIService: AWSLlamaAIService,
  ) {}

  async getAllEnterpriseQuests(
    user: UserEntity,
    filterQueries: getAllQuestsHRfilterQueriesInterface,
  ): Promise<GetAllQuestsHrResDTO> {
    const {
      title,
      page = '1',
      limit = '10',
      scope,
      isActive,
      report,
    } = filterQueries;

    const { limit: take, offset: skip } =
      this.hrUtilsService.parsePageNumberAndGetlimitAndOffset(
        page,
        parseInt(limit, 10),
      );

    // If scope is specifically 'product_hunt', only fetch product hunt quests
    if (scope === 'product_hunt') {
      const productHuntWhere: any = {
        enterpriseId: user.enterprise.id,
        isDeleted: false,
      };
      if (title) productHuntWhere.title = Like(`%${title}%`);
      if (isActive) productHuntWhere.isActive = isActive === 'true';
      if (report && report === 'true') {
        productHuntWhere.numOfReports = MoreThan(0);
      }

      const [productHuntQuests, productHuntTotal] =
        await this.productQuestRepo.findAndCount({
          where: productHuntWhere,
          relations: ['questType', 'user'],
          order: { createdAt: 'DESC' },
          take,
          skip,
        });

      const productHuntQuestsResp = productHuntQuests.map((pq) => {
        const dto = new QuestDTO();
        dto.id = pq.id;
        dto.title = pq.title;
        dto.description = pq.description;
        dto.completionCredits = pq.completionCredits;
        dto.startDate = pq.startDate;
        dto.endDate = pq.endDate;
        dto.difficulty = pq.difficulty;
        dto.createdAt = pq.createdAt;
        dto.updatedAt = pq.updatedAt;
        dto.isDeleted = pq.isDeleted;
        dto.isActive = pq.isActive;
        dto.scope = 'product_hunt' as any;
        dto.questType = pq.questType;
        dto.creator = pq.user;
        return dto;
      });

      return {
        error: false,
        total: productHuntTotal,
        nbHits: productHuntQuestsResp.length,
        quests: productHuntQuestsResp,
      };
    }

    // For enterprise or AI quests, or when no scope is specified (all quests)
    const whereCondition: any = {
      enterprise: { id: user.enterprise.id },
      isDeleted: false,
    };

    if (title) {
      whereCondition.title = Like(`%${title}%`);
    }

    if (scope) {
      if (scope === QUEST_SCOPE.AI) {
        whereCondition.scope = QUEST_SCOPE.AI;
      }
      if (scope === QUEST_SCOPE.ENTERPRISE) {
        whereCondition.scope = QUEST_SCOPE.ENTERPRISE;
      }
    }

    if (isActive) {
      whereCondition.isActive = isActive === 'true' ? true : false;
    }

    if (report && report === 'true') {
      whereCondition.numOfReports = MoreThan(0);
    }

    // If no scope is specified, we need to handle pagination differently
    if (!scope) {
      // Get total counts for proper pagination
      const [allQuests, regularTotal] = await this.questRepo.findAndCount({
        where: whereCondition,
        relations: ['enterprise', 'creator', 'media', 'questType'],
        order: {
          createdAt: 'DESC',
        },
      });

      const productHuntWhere: any = {
        enterpriseId: user.enterprise.id,
        isDeleted: false,
      };
      if (title) productHuntWhere.title = Like(`%${title}%`);
      if (isActive) productHuntWhere.isActive = isActive === 'true';
      if (report && report === 'true') {
        productHuntWhere.numOfReports = MoreThan(0);
      }

      const [allProductHuntQuests, productHuntTotal] =
        await this.productQuestRepo.findAndCount({
          where: productHuntWhere,
          relations: ['questType', 'user'],
          order: { createdAt: 'DESC' },
        });

      // Convert product hunt quests to DTOs
      const productHuntQuestsResp = allProductHuntQuests.map((pq) => {
        const dto = new QuestDTO();
        dto.id = pq.id;
        dto.title = pq.title;
        dto.description = pq.description;
        dto.completionCredits = pq.completionCredits;
        dto.startDate = pq.startDate;
        dto.endDate = pq.endDate;
        dto.difficulty = pq.difficulty;
        dto.createdAt = pq.createdAt;
        dto.updatedAt = pq.updatedAt;
        dto.isDeleted = pq.isDeleted;
        dto.isActive = pq.isActive;
        dto.scope = 'product_hunt' as any;
        dto.questType = pq.questType;
        dto.creator = pq.user;
        return dto;
      });

      // Merge and sort all quests by creation date
      const questsResp = allQuests.map((quest) =>
        QuestDTO.transform(quest, false),
      );
      
      const mergedQuests = [...questsResp, ...productHuntQuestsResp];
      mergedQuests.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      // Apply pagination to merged results
      const paginatedQuests = mergedQuests.slice(skip, skip + take);
      const totalMerged = regularTotal + productHuntTotal;

      return {
        error: false,
        total: totalMerged,
        nbHits: paginatedQuests.length,
        quests: paginatedQuests,
      };
    }

    // For scoped queries (enterprise or ai), use regular pagination
    const [allQuests, total] = await this.questRepo.findAndCount({
      where: whereCondition,
      relations: ['enterprise', 'creator', 'media', 'questType'],
      order: {
        createdAt: 'DESC',
      },
      take,
      skip,
    });

    const questsResp = allQuests.map((quest) =>
      QuestDTO.transform(quest, false),
    );

    return {
      error: false,
      total,
      nbHits: questsResp.length,
      quests: questsResp,
    };
  }

  // -----------------------------------------------------------------------------------------

  async createEnterpriseQuest(
    HR: UserEntity,
    questData: CreateQuestReqDTO,
    quest_media: Express.Multer.File[],
  ): Promise<CreateQuestResDTO> {
    try {
      const {
        title,
        description,
        completionCredits,
        questTypeId,
        difficulty,
        submissionMediaType,
        endDate,
        customQuestName,
        tags,
      } = questData;

      let quest = new QuestEntity();

      quest.title = title;
      quest.description = description;
      quest.difficulty = difficulty;
      quest.submissionMediaType = submissionMediaType;

      if (completionCredits <= 10) {
        throw new BadRequestException(
          'completion credits must be greater than 10.',
        );
      }

      quest.completionCredits = completionCredits;

      const Quest_Type = await this.questTypeRepo.findOneBy({
        id: questTypeId,
      });

      if (!Quest_Type) {
        throw new BadRequestException(
          `Quest Type with id ${questTypeId} not found !!`,
        );
      }

      quest.questType = Quest_Type;

      if (Quest_Type.value === 'CUSTOM_QUEST' && customQuestName) {
        quest.customQuestName = customQuestName;
      }

      if (tags && Array.isArray(tags) && tags.length > 0) {
        quest.tags = tags;
      }

      quest.scope = QUEST_SCOPE.ENTERPRISE;
      quest.creator = HR;
      quest.enterprise = HR.enterprise;

      const startDate = new Date();
      startDate.setHours(0, 0, 0, 0);

      quest.startDate = startDate;

      const parsedEndDate = new Date(endDate);

      if (isNaN(parsedEndDate.getTime())) {
        throw new Error(
          'The end date provided is invalid. Please check and try again.',
        );
      }

      if (parsedEndDate <= startDate) {
        throw new BadRequestException(
          'The end date must be later than the start date. Please provide a valid end date.',
        );
      }

      quest.endDate = parsedEndDate;

      await this.questRepo.save(quest);

      const media = await this.uploadAndGetMediaUrls(quest_media, quest);
      quest.media = media;

      quest = await this.questRepo.save(quest);

      const enterprise = await this.enterpriseRepo.findOneBy({
        id: HR.enterprise.id,
      });
      enterprise.numOfQuests++;
      await this.enterpriseRepo.save(enterprise);

      const respQuest = QuestDTO.transform(quest, false);

      // Create response DTO
      return {
        error: false,
        msg: 'Quest created successfully.',
        quest: respQuest,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async uploadAndGetMediaUrls(
    quest_media: Express.Multer.File[],
    quest: QuestEntity,
  ): Promise<QuestMediaEntity[]> {
    //
    const media = await Promise.all(
      quest_media.map(async (item) => {
        // initializing quest media obj
        const new_quest_media = new QuestMediaEntity();

        // getting file extension
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        // checking and adding file type
        if (AllowedVideoExtensions.includes(fileExt)) {
          new_quest_media.type = QUEST_MEDIA_TYPE.VIDEO;
        } else if (AllowedImageExtensions.includes(fileExt)) {
          new_quest_media.type = QUEST_MEDIA_TYPE.IMAGE;
        } else {
          throw new BadRequestException(`Unsupported file type: ${fileExt}`);
        }

        // Upload file to S3 and store the URL
        const uploadData = await this.s3Service.uploadFile(item);
        new_quest_media.url = uploadData.Location;

        new_quest_media.quest = quest;
        return this.questMediaRepo.save(new_quest_media);
      }),
    );

    return media;
  }

  async deleteEnterpriseQuest(
    user: UserEntity,
    questId: string,
  ): Promise<DeleteQuestResDTO> {
    try {
      const id = this.hrUtilsService.validateAndGetId(questId);

      return await this.dataSource.transaction(
        async (manager: EntityManager) => {
          let quest = await this.questRepo.findOne({
            where: {
              enterprise: { id: user.enterprise.id },
              scope: QUEST_SCOPE.ENTERPRISE,
              id: id,
            },
            relations: [
              'media',
              'creator',
              'enterprise',
              'publishedFeeds',
              'reports',
              'participants',
            ],
          });

          if (!quest || quest.isDeleted === true) {
            throw new NotFoundException('Quest not found !!');
          }

          if (quest.enterprise.id !== user.enterprise.id) {
            throw new UnauthorizedException(
              'This account is not authorized to delete this quest',
            );
          }

          await this.deleteQuestMedia(quest, manager);
          await this.deleteQuestParticipants(quest.participants, manager);
          await this.deleteQuestFeeds(quest.id, manager);
          await this.deleteQuestReports(quest, manager);

          quest.isDeleted = true;
          quest = await this.questRepo.save(quest);

          const enterprise = await this.enterpriseRepo.findOneBy({
            id: user.enterprise.id,
          });
          enterprise.numOfQuests--;
          await this.enterpriseRepo.save(enterprise);

          const questResp = QuestDTO.transform(quest, false);

          return {
            error: false,
            msg: 'Quest deleted successfully!',
            quest: questResp,
          };
        },
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async deleteQuestMedia(
    quest: QuestEntity,
    manager: EntityManager,
  ): Promise<void> {
    if (quest.media?.length > 0) {
      await Promise.all(
        quest.media.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestParticipants(
    participants: QuestParticipantEntity[],
    manager: EntityManager,
  ): Promise<void> {
    if (participants?.length > 0) {
      await Promise.all(
        participants.map(async (participant) => {
          await this.deleteParticipantCompletionProof(participant, manager);
          participant.isDeleted = true;
          await manager.save(participant);
        }),
      );
    }
  }

  private async deleteParticipantCompletionProof(
    participant: QuestParticipantEntity,
    manager: EntityManager,
  ): Promise<void> {
    const completionMedias = participant.questCompletionProof;
    if (completionMedias?.length > 0) {
      await Promise.all(
        completionMedias.map(async (media) => {
          media.isDeleted = true;
          await manager.save(media);
        }),
      );
    }
  }

  private async deleteQuestFeeds(
    questId: number,
    manager: EntityManager,
  ): Promise<void> {
    const feeds = await this.feedRepo.find({
      where: { quest: { id: questId } },
      relations: ['quest', 'questSubmissionMedia', 'comments', 'interactions'],
    });

    if (feeds.length > 0) {
      await Promise.all(
        feeds.map(async (feed) => {
          await this.deleteFeedAssets(feed, manager);
          feed.isDeleted = true;
          await manager.save(feed);
        }),
      );
    }
  }

  private async deleteFeedAssets(
    feed: FeedEntity,
    manager: EntityManager,
  ): Promise<void> {
    const { media, questSubmissionMedia, comments, interactions } = feed;

    if (media?.length > 0) {
      await Promise.all(
        media.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (questSubmissionMedia?.length > 0) {
      await Promise.all(
        questSubmissionMedia.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }

    if (comments?.length > 0) {
      await Promise.all(
        comments.map(async (comment) => {
          comment.isDeleted = true;
          await manager.save(comment);
        }),
      );
    }

    if (interactions?.length > 0) {
      await Promise.all(
        interactions.map(async (interaction) => {
          interaction.isDeleted = true;
          await manager.save(interaction);
        }),
      );
    }
  }

  private async deleteQuestReports(quest: QuestEntity, manager: EntityManager) {
    if (quest.reports.length > 0) {
      await Promise.all(
        quest.reports.map(async (item) => {
          item.isDeleted = true;
          await manager.save(item);
        }),
      );
    }
  }

  async generateMCQs(
    user: UserEntity,
    content: string,
    difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard',
    numQuestions?: number,
  ): Promise<{
    error: boolean;
    questions: MCQQuestion[];
    difficulty: string;
  }> {
    try {
      // Validate content
      if (!content || content.trim().length === 0) {
        throw new BadRequestException('Content is required to generate MCQs');
      }

      const trimmedContent = content.trim();
      if (trimmedContent.length < 500) {
        throw new BadRequestException(
          'Content must be at least 500 characters long. Please provide more detailed content.',
        );
      }

      const validatedNumQuestions = numQuestions
        ? Math.min(Math.max(numQuestions, 5), 10)
        : 5;

      const mcqs = await this.mcqGenerationService.generateMCQsFromContent(
        content,
        difficulty,
        validatedNumQuestions, 
      );

      this.logger.log(
        `Successfully generated ${mcqs.length} MCQs for user ${user.id} (requested: ${validatedNumQuestions})`,
      );

      return {
        error: false,
        questions: mcqs,
        difficulty: difficulty,
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to generate MCQs: ${error.message}`,
        path: `/hr/quests/mcq/generate`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(
        `Failed to generate MCQs: ${error.message}`,
      );
    }
  }

  async acceptMCQs(
    user: UserEntity,
    dto: AcceptMCQDto,
  ): Promise<{
    error: boolean;
    message: string;
    questId?: number;
  }> {
    try {
      // Validate input data
      if (
        !dto.questions ||
        !Array.isArray(dto.questions) ||
        dto.questions.length === 0
      ) {
        throw new BadRequestException('No MCQ questions provided');
      } 
      let quest: QuestEntity;

      if (dto.questId && dto.questId > 0) {
        this.logger.log(`Finding existing quest with ID ${dto.questId}`);
        const existingQuest = await this.questRepo.findOne({
          where: { id: dto.questId },
        });
        if (existingQuest) {
          this.logger.log(
            `Found existing quest with ID ${existingQuest.id}, updating it`,
          );
          quest = existingQuest;

          if (dto.title) quest.title = dto.title;
          if (dto.description) quest.description = dto.description;

          if (dto.tags !== undefined) {
            quest.tags = dto.tags;
            this.logger.log(
              `Updated quest tags to: ${JSON.stringify(dto.tags)}`,
            );
          }

          if (dto.questTypeId) {
            try {
              const questType = await this.questTypeRepo.findOne({
                where: { id: dto.questTypeId },
              });
              if (questType) {
                quest.questType = questType;
                if (questType.value === 'CUSTOM_QUEST') {
                  quest.customQuestName =
                    dto.customQuestName || quest.customQuestName;
                } else {
                  quest.customQuestName = null; 
                }
                this.logger.log(
                  `Updated quest type to: ${questType.value} with custom name: ${quest.customQuestName || 'null'}`,
                );
              }
            } catch (error) {
              this.logger.warn(`Failed to update quest type: ${error.message}`);
            }
          }

          await this.questRepo.save(quest);
        } else {
          this.logger.log(
            `No existing quest found with ID ${dto.questId}, creating new quest`,
          );
          quest = await this.createNewQuestForMCQ(user, dto);
        }
      } else {
        this.logger.log('No questId provided, creating new quest');
        quest = await this.createNewQuestForMCQ(user, dto);
      }

      if (quest.id) {
        const existingQuestions = await this.mcqQuestionRepo.find({
          where: { quest: { id: quest.id } },
        });

        if (existingQuestions.length > 0) {
          this.logger.log(
            `Removing ${existingQuestions.length} existing MCQ questions for quest ${quest.id}`,
          );
          await this.mcqQuestionRepo.remove(existingQuestions);
        }
      } 
      const mcqEntities = dto.questions.map((mcq) => {
        return this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options || [],
          correctAnswers: mcq.correctAnswers || [0],
          difficulty: this.mapDifficultyStringToEnum(
            mcq.difficulty || 'intermediate',
          ),
          quest: quest,
        });
      });

      await this.mcqQuestionRepo.save(mcqEntities);

      this.logger.log(
        `Successfully saved quest "${dto.title || quest.title}" with ${mcqEntities.length} MCQ questions for user ${user.id}`,
      );

      return {
        error: false,
        message: 'MCQs accepted and quest created successfully',
        questId: quest.id,
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to accept MCQs: ${error.message}`,
        path: `/hr/quests/mcq/accept`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(`Failed to accept MCQs: ${error.message}`);
    }
  }
  private async createNewQuestForMCQ(
    user: UserEntity,
    dto: AcceptMCQDto,
  ): Promise<QuestEntity> {
    let questType = null;

    if (dto.questTypeId) {
      try {
        questType = await this.questTypeRepo.findOne({
          where: { id: dto.questTypeId },
        });
        this.logger.log(
          `Found quest type: ${questType?.value} for ID ${dto.questTypeId}`,
        );
      } catch (error) {
        this.logger.warn(
          `Failed to find quest type with ID ${dto.questTypeId}: ${error.message}`,
        );
      }
    }

    if (!questType) {
      try {
        questType = await this.questTypeRepo.findOne({
          where: { value: 'CUSTOM_QUEST' },
        });
        this.logger.log('Using default CUSTOM_QUEST type for MCQ quest');
      } catch (error) {
        this.logger.warn(`Failed to find CUSTOM_QUEST type: ${error.message}`);
      }
    }

    let endDate: Date;
    if (dto.endDate) {
      endDate = new Date(dto.endDate);
      if (isNaN(endDate.getTime())) {
        throw new BadRequestException('Invalid end date provided');
      }
      if (endDate <= new Date()) {
        throw new BadRequestException('End date must be in the future');
      }
    } else {
      endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); 
    }

    const quest = this.questRepo.create({
      title: dto.title || 'MCQ Quest',
      description: dto.description || 'Multiple choice questions',
      difficulty: this.mapDifficultyStringToEnum(
        dto.questions[0]?.difficulty || 'intermediate',
      ),
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE,
      isActive: true,
      startDate: new Date(),
      endDate: endDate,
      completionCredits: dto.completionCredits, 
      enterprise: user.enterprise,
      creator: user,
      questType: questType,
      customQuestName:
        questType?.value === 'CUSTOM_QUEST' ? dto.customQuestName : null,
      tags: dto.tags || [],
    });

    this.logger.log(
      `Creating MCQ quest with type: ${questType?.value || 'null'}, credits: ${quest.completionCredits} and custom name: ${quest.customQuestName || 'null'}`,
    );

    return await this.questRepo.save(quest);
  }

  private mapDifficultyStringToEnum(
    difficulty: string,
  ): QUEST_DIFFICULTY_TYPES {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return QUEST_DIFFICULTY_TYPES.EASY;
      case 'intermediate':
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
      case 'hard':
        return QUEST_DIFFICULTY_TYPES.HARD;
      case 'very hard':
        return QUEST_DIFFICULTY_TYPES.VERY_HARD;
      default:
        return QUEST_DIFFICULTY_TYPES.INTERMEDIATE;
    }
  }

  async rejectMCQs(
    user: UserEntity,
    questId: string,
  ): Promise<{
    error: boolean;
    message: string;
  }> {
    try {
      const id = parseInt(questId, 10);
      if (isNaN(id)) {
        throw new BadRequestException('Invalid quest ID provided');
      }

      const quest = await this.questRepo.findOne({
        where: {
          id,
          enterprise: { id: user.enterprise.id },
          creator: { id: user.id },
        },
        relations: ['mcqQuestions', 'enterprise', 'creator'],
      });

      if (!quest) {
        throw new BadRequestException(
          'Quest not found or you do not have permission to modify it',
        );
      }

      await this.dataSource.transaction(async (manager) => {
        if (quest.mcqQuestions && quest.mcqQuestions.length > 0) {
          await manager.remove(MCQQuestionEntity, quest.mcqQuestions);
        }

        await manager.remove(QuestEntity, quest);
      });

      this.logger.log(
        `Successfully rejected and removed quest ID ${questId} with associated MCQs for user ${user.id}`,
      );

      return {
        error: false,
        message: 'MCQs rejected and quest removed successfully',
      };
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to reject MCQs: ${error.message}`,
        path: `/hr/quests/mcq/reject/${questId}`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new BadRequestException(`Failed to reject MCQs: ${error.message}`);
    }
  }



  async createQuestForProductUsingTextAndPdf(
    user: UserEntity,
    productSummaryData: any,
    questMedia: Express.Multer.File,
  ) {
    const { productSummary } = productSummaryData;

    if (!productSummary && !questMedia) {
      throw new BadRequestException(
        'Either productSummary or questMedia must be provided',
      );
    }

    if (productSummary && questMedia) {
      throw new BadRequestException(
        'Cannot provide both productSummary and questMedia - only one should be provided',
      );
    }

    // Handling text
    if (productSummary) {
      if (!productSummaryData.questType) {
        throw new BadRequestException("Field 'questType' is required");
      }

      if (!productSummaryData.description) {
        throw new BadRequestException("Field 'description' is required");
      }

      if (!productSummaryData.completionCredit) {
        throw new BadRequestException("Field 'completionCredit' is required");
      }

      if (!productSummaryData.endDate) {
        throw new BadRequestException("Field 'endDate' is required");
      }

      if (!productSummaryData.difficulty) {
        throw new BadRequestException("Field 'difficulty' is required");
      }

      const questionAnswerForProductSummary =
        await this.aWSLlamaAIService.generateQuestBasedOnProductSummaryForText(
          productSummary,
          productSummaryData.difficulty,
        );

      if (!questionAnswerForProductSummary) {
        throw new BadRequestException(
          'Failed to generate question and answer for product summary',
        );
      }

      const productSummaryDataSaved = await this.savingDataToProductSummaryToDb(
        productSummaryData,
        user,
      );

      const savedProductQuest = await this.saveQuestInDbForProductSummary(
        user,
        productSummaryDataSaved.id,
        productSummaryDataSaved.title,
        productSummaryData,
      );

      await this.saveQuestionAnswerInDbForProductSummary(
        savedProductQuest.id,
        questionAnswerForProductSummary,
      );

      return {
        title: productSummaryDataSaved.title,
        question: questionAnswerForProductSummary.question,
        answer: questionAnswerForProductSummary.answer,
        difficulty: questionAnswerForProductSummary.difficulty,
        productQuestId: savedProductQuest.id,
        productSummaryId: productSummaryDataSaved.id,
      };
    }

    // handling media to extract text
    if (questMedia) {
    }
  }

  async createQuestForProductUsingTextAndPdfForMCQ(
    user: UserEntity,
    data: any,
    questMedia: Express.Multer.File,
  ) {
    const { productSummary, numberOfQuestions } = data; 

    const adjustedNumberOfQuestions =
      numberOfQuestions < 5 || !numberOfQuestions ? 5 : numberOfQuestions;

    if (!productSummary && !questMedia) {
      throw new BadRequestException(
        'Either productSummary or questMedia must be provided',
      );
    }
    if (productSummary && questMedia) {
      throw new BadRequestException(
        'Cannot provide both productSummary and questMedia - only one should be provided',
      );
    }

    if (productSummary) {
      if (!data.questType) {
        throw new BadRequestException("Field 'questType' is required");
      }

      if (!data.description) {
        throw new BadRequestException("Field 'description' is required");
      }

      if (!data.completionCredit) {
        throw new BadRequestException("Field 'completionCredit' is required");
      }

      if (!data.endDate) {
        throw new BadRequestException("Field 'endDate' is required");
      }

      if (!data.difficulty) {
        throw new BadRequestException("Field 'difficulty' is required");
      }

      try {
        const mcqQuestions =
          await this.aWSLlamaAIService.generateQuestBasedOnProductSummaryForMCQ(
            productSummary,
            adjustedNumberOfQuestions,
            data.difficulty,
          );

        if (!mcqQuestions || mcqQuestions.length === 0) {
          throw new BadRequestException(
            'Failed to generate MCQ questions. Please try again.',
          );
        }

        const productSummaryDataSaved =
          await this.savingDataToProductSummaryToDb(data, user);

        const savedQuestForMCQ = await this.saveMCQQuestforProductSummary(
          productSummaryDataSaved,
          user,
          data.tags,
          mcqQuestions,
        );

        await this.saveMCQInDbForProductSummary(
          mcqQuestions,
          savedQuestForMCQ.id,
          productSummaryDataSaved.id,
          user,
        );

        this.logger.log(
          `Successfully generated ${mcqQuestions.length} MCQ questions for product summary`,
        );
        return {
          success: true,
          questions: mcqQuestions,
          count: mcqQuestions.length,
        };
      } catch (error) {
        this.logger.error({
          error: true,
          errorId: Date.now(),
          statusCode: 500,
          timestamp: new Date(),
          path: '/hr/quests/product-based/mcq/quest/create',
          message: `Error generating MCQ questions: ${error.message}`,
        });
        if (error instanceof BadRequestException) {
          throw error;
        }
        throw new BadRequestException(
          `Failed to generate MCQ questions: ${error.message}`,
        );
      }
    }

    if (questMedia) {
    }
  }

  async savingDataToProductSummaryToDb(
    productSummaryData: any,
    user: UserEntity,
  ): Promise<ProductSummaryEntity> {
    try {
      const { productSummary } = productSummaryData;

      const productSummaryEntity = new ProductSummaryEntity();
      productSummaryEntity.summary = productSummary;
      productSummaryEntity.title =
        await this.aWSLlamaAIService.generateQuestTitleBasedOnContext(
          productSummary,
        );
      productSummaryEntity.enterpriseId = user?.enterprise?.id;
      productSummaryEntity.uploadedBy = user;

      const savedProductSummary =
        await this.productSummaryRepo.save(productSummaryEntity);

      this.logger.log(
        `Successfully saved product summary to database with ID: ${savedProductSummary.id} for user ${user.id}`,
      );

      return savedProductSummary;
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-based/quest/create',
        message: `Error saving product summary to database: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to save product summary to database: ${error.message}`,
      );
    }
  }

  async saveQuestInDbForProductSummary(
    user: UserEntity,
    productSummaryDataSavedId: number,
    productSummaryDataSavedTitle: string,
    productSummaryData: any,
  ): Promise<ProductQuestEntity> {
    try {
      const productQuest = new ProductQuestEntity();
      productQuest.title = productSummaryDataSavedTitle;
      productQuest.difficulty = productSummaryData.difficulty.toLowerCase();
      productQuest.submissionType = PRODUCT_QUEST_SUBMISSION_TYPE.TEXT;
      productQuest.completionCredits = 200;
      productQuest.startDate = new Date();
      productQuest.endDate = new Date(
        Date.now() + new Date(productSummaryData.endDate).getTime(),
      );
      productQuest.isActive = true;
      productQuest.productSummaryId = productSummaryDataSavedId;
      productQuest.enterpriseId = user.enterprise.id;
      productQuest.assignToUserId = user.id;
      productQuest.description = productSummaryData.description;
      productQuest.tags = productSummaryData.tags
        ? productSummaryData.tags
        : ['Employee'];

      const savedProductQuest = await this.productQuestRepo.save(productQuest);

      this.logger.log(
        `Successfully saved product quest to database with ID: ${savedProductQuest.id} for product summary ${productSummaryDataSavedId}`,
      );

      return savedProductQuest;
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-based/quest/create',
        message: `Error saving product quest to database: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to save product quest to database: ${error.message}`,
      );
    }
  }

  async saveQuestionAnswerInDbForProductSummary(
    questId: number,
    questionAnswerForProductSummary: any,
  ) {
    try {
      const productQuestionAnswerRepo = this.dataSource.getRepository(
        'ProductQuestionAnswerEntity',
      );
      const entity = productQuestionAnswerRepo.create({
        question: questionAnswerForProductSummary.question,
        answer: questionAnswerForProductSummary.answer,
        productQuestId: questId,
      });
      const saved = await productQuestionAnswerRepo.save(entity);
      this.logger.log(
        `Saved ProductQuestionAnswerEntity with ID ${saved.id} for quest ID ${questId}`,
      );
      return saved;
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-based/question-answer/save',
        message: `Error saving product question/answer: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to save product question/answer: ${error.message}`,
      );
    }
  }

  async saveMCQQuestforProductSummary(
    productSummarySavedData: any,
    user: UserEntity,
    tags: any,
    mcqQuestions: any[],
  ) {
    try {
      const productSummaryId = productSummarySavedData.id;
      const enterpriseId = user.enterprise.id;
      const userId = user.id;
      const createdBy = user;
      const title = productSummarySavedData.title || 'MCQ Quest';
      const description =
        productSummarySavedData.description || 'MCQ quest for product summary';
      const difficulty = productSummarySavedData.difficulty || 'intermediate';
      const completionCredits = productSummarySavedData.completionCredit || 100;
      const startDate = new Date();
      const endDate = productSummarySavedData.endDate
        ? new Date(productSummarySavedData.endDate)
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      const submissionType = PRODUCT_QUEST_SUBMISSION_TYPE.MCQ;
      const productQuest = this.productQuestRepo.create({
        title,
        submissionType,
        description,
        difficulty,
        completionCredits,
        startDate,
        endDate,
        isActive: true,
        productSummaryId,
        enterpriseId,
        assignToUserId: userId,
        tags: !tags ? ['Employee'] : tags,
      });
      const savedProductQuest = await this.productQuestRepo.save(productQuest);
      if (
        !mcqQuestions ||
        !Array.isArray(mcqQuestions) ||
        mcqQuestions.length === 0
      ) {
        this.logger.warn('No MCQ questions found to save for product summary');
        return;
      }
      return savedProductQuest;
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-based/mcq/save',
        message: `Error saving MCQ quest for product summary: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to save MCQ quest for product summary: ${error.message}`,
      );
    }
  }

  async saveMCQInDbForProductSummary(
    mcqQuestions: any,
    questId: number,
    productSummaryId: number,
    user: UserEntity,
  ) {
    const mcqEntities = mcqQuestions.map((mcq) => {
      return this.productMCQQuestRepo.create({
        question: mcq.question,
        options: mcq.options,
        correctAnswers: mcq.correctAnswers,
        difficulty: mcq.difficulty || 'intermediate',
        productSummaryId,
        enterpriseId: user.enterprise.id,
        assignToUserId: user,
        questId,
      });
    });
    await this.productMCQQuestRepo.save(mcqEntities);
  }

  async createProductSummary(
    user: UserEntity,
    productSummaryData: any,
  ): Promise<ProductSummaryEntity> {
    try {
      const productSummary = new ProductSummaryEntity();
      productSummary.title = productSummaryData.title;
      productSummary.content = productSummaryData.content;
      productSummary.summary = productSummaryData.summary;
      (productSummary.structuredContentJson =
        productSummary.structuredContentJson),
        (productSummary.imageAnalysesJson = productSummary.imageAnalysesJson),
        (productSummary.extractedImageUrls = productSummary.extractedImageUrls),
        (productSummary.enterpriseId = user.enterprise.id);
      productSummary.uploadedByUserId = user.id;
      productSummary.isEnableToView =
        productSummaryData.isEnableToView ?? false;
      productSummary.isDeleted = false;

      const saved = await this.productSummaryRepo.save(productSummary);
      this.logger.log(
        `ProductSummary created with ID: ${saved.id} by user ${user.id}`,
      );
      return saved;
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-summary',
        message: `Error saving product summary: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to save product summary: ${error.message}`,
      );
    }
  }

  async getAllProductSummaries(user: UserEntity): Promise<{
    error: boolean;
    total: number;
    productSummaries: ProductSummaryEntity[];
  }> {
    try {
      const [productSummaries, total] = await this.productSummaryRepo
        .createQueryBuilder('productSummary')
        .leftJoinAndSelect('productSummary.uploadedBy', 'uploadedBy')
        .leftJoinAndSelect('uploadedBy.enterprise', 'enterprise')
        .where('productSummary.enterpriseId = :enterpriseId', {
          enterpriseId: user.enterprise.id,
        })
        .andWhere('productSummary.isDeleted = :isDeleted', {
          isDeleted: false,
        })
        .select([
          'productSummary.id',
          'productSummary.title',
          'productSummary.summary',
          'productSummary.enterpriseId',
          'productSummary.isEnableToView',
          'productSummary.isDeleted',
          'productSummary.createdAt',
          'productSummary.updatedAt',
          'uploadedBy.id',
          'uploadedBy.firstName',
          'uploadedBy.lastName',
          'uploadedBy.email',
          'uploadedBy.avatar',
          'enterprise.id',
          'enterprise.name',
        ])
        .orderBy('productSummary.createdAt', 'DESC')
        .getManyAndCount();

      this.logger.log(
        `Retrieved ${total} product summaries for enterprise ${user.enterprise.id}`,
      );

      return {
        error: false,
        total,
        productSummaries,
      };
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: '/hr/quests/product-summary',
        message: `Error retrieving product summaries: ${error.message}`,
      });
      throw new BadRequestException(
        `Failed to retrieve product summaries: ${error.message}`,
      );
    }
  }
}
