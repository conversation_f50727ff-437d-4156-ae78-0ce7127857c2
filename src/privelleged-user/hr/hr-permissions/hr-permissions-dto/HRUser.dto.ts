import {
  PermissionCategory,
  PermissionEntity,
  UserEntity,
} from 'src/models/user-entity';
import { HRUser_RoleDTO } from './HRUser_Role.dto';
import { HRUserDTO_PermissionDTO } from './HRUser_Permission.dto';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';

export class HRUserDTO {
  id: string;
  username: string;
  email: string;
  avatar: string;
  createdAt: Date;
  updatedAt: Date;
  password: string;
  role: HRUser_RoleDTO;
  permissions: HRUserDTO_PermissionDTO[];

  static transform(object: UserEntity): HRUserDTO {
    const transformedObj: HRUserDTO = new HRUserDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.username = object.firstName + object.lastName;
    transformedObj.email = object.email;
    transformedObj.avatar = object.avatar;
    transformedObj.createdAt = new Date();
    transformedObj.updatedAt = new Date();
    transformedObj.password = object.password;

    if (object.roles) {
      const HRroleIndex = object.roles.findIndex(
        (item) => item.value === ROLE_VALUES.HR,
      );

      if (HRroleIndex != -1) {
        transformedObj.role = HRUser_RoleDTO.transform(
          object.roles[HRroleIndex],
        );

        transformedObj.permissions = this.groupPermissionsByCategory(
          object.roles[HRroleIndex].permissions,
        );
      }

      // Add HR dashboard permissions as a static entry
      transformedObj.permissions.unshift(this.getDashboardPermission());
      if (object.enterprise?.isRewardsEnabled) {
        transformedObj.permissions.push(this.getRewardSelectionPermission());
      }
    }

    return transformedObj;
  }

  static groupPermissionsByCategory(
    permissions: PermissionEntity[],
  ): HRUserDTO_PermissionDTO[] {
    const grouped: {
      [key in PermissionCategory]?: HRUserDTO_PermissionDTO;
    } = {};

    // Group permissions by category
    permissions.forEach((permission, index) => {
      const category = permission.category;

      if (HR_Permissions_Routes.includes(category)) {
        if (!grouped[category]) {
          grouped[category] = this.createCategoryPermission(category, index);
          const children = this.getChildrenRouteData(category);
          grouped[category].children.push(...children);
        }
      }
    });

    return Object.values(grouped);
  }

  static createCategoryPermission(
    category: PermissionCategory,
    index: number,
  ): HRUserDTO_PermissionDTO {
    return {
      id: `${index + 1}`,
      label: this.capitalizeFirstLetter(category),
      parentId: '',
      name: this.capitalizeFirstLetter(category),
      icon: iconCategoryMap[category] || '  bxs:hand-right',
      type: 0,
      route: `/${category}`,
      //   component: `/dashboard/HR/${category}/index.tsx`,
      order: index + 2,
      category: category,
      children: [],
    };
  }

  static capitalizeFirstLetter(text: string): string {
    return text
      .split(' ') // Split the string into an array of words
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize the first letter of each word
      .join(' '); // Join the words back into a string
  }

  static getChildrenRouteData(
    category: PermissionCategory,
  ): HRUserDTO_PermissionDTO[] {
    const categoryRoutes = permissionsRouteData[category];

    return categoryRoutes.map((item, index) => ({
      id: `${index + 1}`,
      label: this.capitalizeFirstLetter(item.label),
      parentId: '',
      name: this.capitalizeFirstLetter(item.name),
      icon: item.icon || 'mdi-light:arrow-right',
      type: 1, // used for children
      route: `/${category}/${item.route}`,
      component: item.component,
      order: index + 2,
      children: [],
    }));
  }

  static getDashboardPermission(): HRUserDTO_PermissionDTO {
    return {
      id: '0',
      label: 'Dashboard',
      parentId: '',
      name: 'Dashboard',
      icon: 'bxs:dashboard',
      type: 1,
      route: '/dashboard',
      component: '/dashboard/HR/workbench/index.tsx',
      order: 1,
      category: PermissionCategory.HR,
    };
  }

  static getRewardSelectionPermission(): HRUserDTO_PermissionDTO {
    return {
      id: 'reward-selection',
      label: 'Rewards',
      parentId: '',
      name: 'Reward Selection',
      icon: 'bxs:award',
      type: 1,
      route: '/dashboard/hr/rewards',
      component: '/dashboard/HR/rewards/index.tsx',
      order: 2,
      category: PermissionCategory.HR,
    };
  }
}

// ------------------------------------------------------------------------------

interface permissionsRouteDataElementsInterface {
  label: string;
  name: string;
  route: string;
  component: string;
  icon?: string;
}

interface permissionsRouteDataInterface {
  [PermissionCategory.USERS]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.QUEST]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.FEED]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.DEPARTMENT]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.HYPERLINK]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.PRODUCT_HUNT]: permissionsRouteDataElementsInterface[];
}

export const HR_Permissions_Routes: PermissionCategory[] = [
  PermissionCategory.USERS,
  PermissionCategory.QUEST,
  PermissionCategory.FEED,
  PermissionCategory.DEPARTMENT,
  PermissionCategory.HYPERLINK,
  PermissionCategory.PRODUCT_HUNT,
];

const addIcon = 'fluent:add-24-filled';
const listIcon = 'heroicons:list-bullet-solid';

const iconCategoryMap = {
  [PermissionCategory.USERS]: 'heroicons:users-16-solid',
  [PermissionCategory.QUEST]: 'fluent-mdl2:cube-shape-solid',
  [PermissionCategory.FEED]: 'bi:image',
  [PermissionCategory.DEPARTMENT]: 'entypo:briefcase',
  [PermissionCategory.HYPERLINK]: 'mingcute:link-line',
  [PermissionCategory.PRODUCT_HUNT]: 'fluent:apps-list-detail-32-filled',
};

const permissionsRouteData: permissionsRouteDataInterface = {
  [PermissionCategory.USERS]: [
    {
      label: 'List Users',
      name: 'List Users',
      route: 'list_users',
      icon: listIcon,
      component: '/dashboard/HR/users/list/index.tsx',
    },
    {
      label: 'Add Users',
      name: 'Add Users',
      route: 'add_users',
      icon: addIcon,
      component: '/dashboard/HR/users/add/index.tsx',
    },
  ],
  [PermissionCategory.QUEST]: [
    {
      label: 'List Quests',
      name: 'List Quests',
      route: 'list_quests',
      icon: listIcon,
      component: '/dashboard/HR/quests/list/index.tsx',
    },
    {
      label: 'Add Quests',
      name: 'Add Quests',
      route: 'add_quests',
      icon: addIcon,
      component: '/dashboard/HR/quests/add/index.tsx',
    },
  ],
  [PermissionCategory.FEED]: [
    {
      label: 'List Feeds',
      name: 'List Feeds',
      route: 'list_feeds',
      icon: listIcon,
      component: '/dashboard/HR/feeds/list/index.tsx',
    },
  ],
  [PermissionCategory.DEPARTMENT]: [
    {
      label: 'List Departments',
      name: 'List Departments',
      route: 'list_departments',
      icon: listIcon,
      component: '/dashboard/HR/departments/list/index.tsx',
    },
    {
      label: 'Add Departments',
      name: 'Add Departments',
      route: 'add_departments',
      icon: addIcon,
      component: '/dashboard/HR/departments/add/index.tsx',
    },
  ],
  [PermissionCategory.HYPERLINK]: [
    {
      label: 'List Hyperlinks',
      name: 'List Hyperlinks',
      route: 'list_hyperlinks',
      icon: listIcon,
      component: '/dashboard/HR/hyperlinks/list/index.tsx',
    },
    {
      label: 'Add Hyperlinks',
      name: 'Add Hyperlinks',
      route: 'add_hyperlinks',
      icon: addIcon,
      component: '/dashboard/HR/hyperlinks/add/index.tsx',
    },
  ],
  [PermissionCategory.PRODUCT_HUNT]: [
    {
      label: 'Products List',
      name: 'Products List',
      route: 'products_list',
      icon: listIcon,
      component: '/dashboard/HR/product_hunt/products_list/index.tsx',
    },
    {
      label: 'Add Product',
      name: 'Add Product',
      route: 'add_product',
      icon: addIcon,
      component: '/dashboard/HR/product_hunt/add/index.tsx',
    },
  ],
};
