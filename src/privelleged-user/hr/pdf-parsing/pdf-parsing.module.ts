import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { PdfParsingController } from './pdf-parsing.controller';
import {
  PdfParsingService,
  NovaAiService,
  ImageExtractionService,
} from './services';
import { ProductSummaryEntity } from 'src/models/pdf-parsing-entity/parser.entity';
import { UserEntity, AccessTokenEntity } from 'src/models/user-entity';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { LoggerModule } from 'src/common/logger/logger.module';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProductSummaryEntity,
      UserEntity,
      AccessTokenEntity,
    ]),
    ConfigModule,
    LoggerModule,
  ],
  controllers: [PdfParsingController],
  providers: [
    PdfParsingService,
    NovaAiService,
    ImageExtractionService,
    S3Service,
    AuthGuard,
  ],
  exports: [PdfParsingService, NovaAiService, ImageExtractionService],
})
export class PdfParsingModule {}
