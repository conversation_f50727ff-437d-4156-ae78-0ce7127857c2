import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ImageAnalysisDto, ContentSectionDto } from './pdf-parsing-response.dto';

export class UpdateSummaryDto {
  @ApiProperty({
    description: 'Updated title for the PDF document',
    example: 'Updated Product Specification Document',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiProperty({
    description: 'Updated summary content',
    example: 'Updated summary of the document content',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  summary?: string;

  @ApiPropertyOptional({
    description: 'Updated full text content',
    example: 'Updated complete text content from the PDF...',
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({
    description: 'Updated structured content sections',
    type: [ContentSectionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentSectionDto)
  structuredContent?: ContentSectionDto[];

  @ApiPropertyOptional({
    description: 'Updated image analyses',
    type: [ImageAnalysisDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageAnalysisDto)
  imageAnalyses?: ImageAnalysisDto[];

  @ApiPropertyOptional({
    description: 'Updated extracted image URLs',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  extractedImageUrls?: string[];
}
