import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UploadPdfDto {
  @ApiProperty({
    description: 'Title for the PDF document',
    example: 'Product Specification Document',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    description: 'Optional additional context for parsing',
    example: 'Focus on technical specifications and features',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  additionalContext?: string;
}
