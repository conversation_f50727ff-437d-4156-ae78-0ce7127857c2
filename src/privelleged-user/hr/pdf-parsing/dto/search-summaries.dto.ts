import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsO<PERSON>al, IsN<PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class SearchSummariesDto {
  @ApiProperty({
    description: 'Search query to find similar titles',
    example: 'product specification',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  query: string;

  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of results per page (max 20)',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(20)
  limit?: number = 10;
}

export class SearchSummariesResponseDto {
  @ApiProperty({
    description: 'Array of matching product summaries',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        title: { type: 'string', example: 'Product Specification v2.1' },
        summary: { type: 'string', example: 'This document outlines...' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  data: {
    id: number;
    title: string;
    summary: string;
    createdAt: Date;
    updatedAt: Date;
  }[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      total: { type: 'number', example: 25 },
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
