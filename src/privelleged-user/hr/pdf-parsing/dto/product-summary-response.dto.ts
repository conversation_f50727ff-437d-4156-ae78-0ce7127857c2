import { ApiProperty } from '@nestjs/swagger';
import { ImageAnalysisDto, ContentSectionDto } from './pdf-parsing-response.dto';

export class ProductSummaryResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the product summary',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Title of the document',
    example: 'Quarterly Report Q3 2024',
  })
  title: string;

  @ApiProperty({
    description: 'Complete text content from the PDF',
    example: 'QUARTERLY REPORT Q3 2024\n\nExecutive Summary...',
  })
  content: string;

  @ApiProperty({
    description: 'Summary of the document content',
    example: 'This quarterly report demonstrates exceptional growth with 15% revenue increase...',
  })
  summary: string;

  @ApiProperty({
    description: 'Analysis of all images found in the PDF with S3 URLs',
    type: [ImageAnalysisDto],
    required: false,
  })
  imageAnalyses?: ImageAnalysisDto[];

  @ApiProperty({
    description: 'URLs of actual images extracted from the PDF and uploaded to S3',
    type: [String],
    example: [
      'https://bucket.s3.amazonaws.com/images/quarterly-report-img-1.png',
      'https://bucket.s3.amazonaws.com/images/quarterly-report-img-2.jpg'
    ],
    required: false,
  })
  extractedImageUrls?: string[];

  @ApiProperty({
    description: 'Enterprise ID this summary belongs to',
    example: 456,
  })
  enterpriseId: number;

  @ApiProperty({
    description: 'ID of the user who uploaded this document',
    example: 789,
  })
  uploadedByUserId: number;

  @ApiProperty({
    description: 'Date when the summary was created',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the summary was last updated',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Information about the user who uploaded the document',
    required: false,
  })
  uploadedBy?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };

  @ApiProperty({
    description: 'Name of the user who uploaded the document',
    example: 'John Doe',
  })
  uploadedByName?: string;

  // PDF source information for edit functionality
  @ApiProperty({
    description: 'Source type of the PDF',
    example: 's3_url',
    enum: ['upload', 's3_url', 's3_bucket'],
  })
  sourceType?: string;

  @ApiProperty({
    description: 'Source location (S3 URL, bucket/key, or filename)',
    example: 'https://bucket.s3.amazonaws.com/docs/report.pdf',
  })
  sourceLocation?: string;

  @ApiProperty({
    description: 'Original filename of the PDF',
    example: 'quarterly-report-q3-2024.pdf',
  })
  originalFileName?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 2048576,
  })
  fileSizeBytes?: number;

  @ApiProperty({
    description: 'Total number of pages in the PDF',
    example: 15,
  })
  totalPages?: number;
}

export class ProductSummaryListResponseDto {
  @ApiProperty({
    description: 'List of product summaries',
    type: [ProductSummaryResponseDto],
  })
  summaries: ProductSummaryResponseDto[];

  @ApiProperty({
    description: 'Total number of summaries',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}
