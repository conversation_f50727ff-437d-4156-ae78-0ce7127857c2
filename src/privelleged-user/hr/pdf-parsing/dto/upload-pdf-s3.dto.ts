import { IsString, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UploadPdfFromS3Dto {
  @ApiProperty({
    description: 'S3 URL of the PDF document',
    example: 's3://my-bucket/documents/product-spec.pdf',
  })
  @IsString()
  @IsNotEmpty()
  s3Url: string;

  @ApiProperty({
    description: 'Title for the PDF document',
    example: 'Product Specification Document',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    description: 'Optional additional context for parsing',
    example: 'Focus on technical specifications and features',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  additionalContext?: string;
}

export class UploadPdfFromS3BucketKeyDto {
  @ApiProperty({
    description: 'S3 bucket name',
    example: 'my-documents-bucket',
  })
  @IsString()
  @IsNotEmpty()
  bucketName: string;

  @ApiProperty({
    description: 'S3 object key (path to the PDF file)',
    example: 'documents/product-spec.pdf',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Title for the PDF document',
    example: 'Product Specification Document',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    description: 'Optional additional context for parsing',
    example: 'Focus on technical specifications and features',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  additionalContext?: string;
}
