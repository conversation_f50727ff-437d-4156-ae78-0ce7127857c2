import { ApiProperty } from '@nestjs/swagger';

export class ImageAnalysisDto {
  @ApiProperty({
    description: 'Index of the image in the document',
    example: 0,
  })
  imageIndex: number;

  @ApiProperty({
    description: 'Detailed description of the image content',
    example: 'A bar chart showing quarterly sales data with blue bars representing Q1-Q4 revenue',
  })
  description: string;

  @ApiProperty({
    description: 'Topics or themes this image relates to',
    example: ['sales performance', 'quarterly results', 'revenue analysis'],
  })
  associatedTopics: string[];

  @ApiProperty({
    description: 'Page number where the image appears',
    example: 2,
    required: false,
  })
  pageLocation?: number;
  @ApiProperty({
    description: 'How this image relates to surrounding content',
    example: 'This chart supports the discussion about Q3 revenue growth mentioned in the preceding paragraph',
  })
  contextualRelevance: string;

  @ApiProperty({
    description: 'S3 URL of the actual extracted image (if successfully extracted)',
    example: 'https://bucket.s3.amazonaws.com/images/quarterly-report-img-1.png',
    required: false,
  })
  s3Url?: string;

  @ApiProperty({
    description: 'File name of the extracted image in S3',
    example: 'quarterly-report-img-1.png',
    required: false,
  })
  extractedImageFileName?: string;

  @ApiProperty({
    description: 'MIME type of the extracted image',
    example: 'image/png',
    required: false,
  })
  extractedImageType?: string;
}

export class ContentSectionDto {
  @ApiProperty({
    description: 'Section heading or title',
    example: 'Executive Summary',
    required: false,
  })
  heading?: string;

  @ApiProperty({
    description: 'Exact text content from this section',
    example: 'This quarterly report demonstrates exceptional growth...',
  })
  content: string;

  @ApiProperty({
    description: 'Page number where this content appears',
    example: 1,
    required: false,
  })
  pageNumber?: number;

  @ApiProperty({
    description: 'Indices of images related to this content section',
    example: [0, 1],
    required: false,
  })
  relatedImages?: number[];
}

export class PdfParsingResponseDto {
  @ApiProperty({
    description: 'Success status of the parsing operation',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'PDF parsed and comprehensive analysis generated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'ID of the created product summary record',
    example: 21,
  })
  productSummaryId: number;

  @ApiProperty({
    description: 'Complete word-for-word text content from the PDF exactly as it appears',
    example: 'Product Note | eNACH V1.2\nIntroduction E-Mandate and Auto Debit Feature...',
  })
  fullTextContent: string;

  @ApiProperty({
    description: 'Structured content organized by sections with exact text',
    type: [ContentSectionDto],
  })
  structuredContent: ContentSectionDto[];

  @ApiProperty({
    description: 'Indexed analysis of all images found in the PDF with descriptions',
    type: [ImageAnalysisDto],
  })
  imageAnalyses: ImageAnalysisDto[];

  @ApiProperty({
    description: 'URLs of actual images extracted from the PDF and uploaded to S3',
    type: [String],
    example: [
      'https://thrivify-s3-dev-bucket.s3.us-east-2.amazonaws.com/8d45b695-e307-4fd6-965b-9d6e71d88e36.png',
      'https://thrivify-s3-dev-bucket.s3.us-east-2.amazonaws.com/29f008ca-6c0b-4abc-880b-4431f5d833bd.png'
    ],
    required: false,
  })
  extractedImageUrls?: string[];

  @ApiProperty({
    description: 'Comprehensive summary of the document content',
    example: 'The document provides a comprehensive overview of the eNACH process...',
  })
  summary: string;

  @ApiProperty({
    description: 'Processing time in milliseconds',
    example: 114580,
  })
  processingTime: number;

  @ApiProperty({
    description: 'Additional metadata about the processed document',
    example: {
      fileSize: 3341851,
      fileName: 'test jack0168.pdf',
      totalPages: 1,
      totalImages: 12,
      totalExtractedImages: 21
    },
  })
  metadata?: {
    fileSize: number;
    fileName: string;
    totalPages?: number;
    totalImages?: number;
    totalExtractedImages?: number;
  };
}

export class PdfParsingErrorDto {
  @ApiProperty({
    description: 'Success status (always false for errors)',
    example: false,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message',
    example: 'Failed to parse PDF: Invalid file format',
  })
  message: string;

  @ApiProperty({
    description: 'Error code for client handling',
    example: 'INVALID_FILE_FORMAT',
  })
  errorCode: string;
}
