import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { PdfParsingService } from './services/pdf-parsing.service';
import {
  UploadPdfDto,
  UploadPdfFromS3Dto,
  UploadPdfFromS3BucketKeyDto,
  PdfParsingResponseDto,
  PdfParsingErrorDto,
  ProductSummaryResponseDto,
  ProductSummaryListResponseDto,
  UpdateSummaryDto,
  SearchSummariesDto,
  SearchSummariesResponseDto,
} from './dto';
import { ProductSummaryEntity } from 'src/models/pdf-parsing-entity/parser.entity';

@ApiTags('PDF Parsing')
@Controller('pdf-parsing')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class PdfParsingController {
  constructor(private readonly pdfParsingService: PdfParsingService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Upload and parse PDF document',
    description:
      'Upload a PDF file and generate a structured summary using Amazon Nova Lite model',
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    description: 'PDF parsed successfully',
    type: PdfParsingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid file or parameters',
    type: PdfParsingErrorDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: PdfParsingErrorDto,
  })
  async uploadAndParsePdf(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadPdfDto: UploadPdfDto,
    @Request() req: any,
  ): Promise<PdfParsingResponseDto> {
    return await this.pdfParsingService.parsePdfAndCreateSummary(
      file,
      uploadPdfDto,
      req.user.id,
    );
  }

  @Post('upload-background')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Upload PDF document for background processing',
    description:
      'Upload a PDF file and start background processing using Amazon Nova Lite model. Returns immediately with processing status.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 202,
    description: 'PDF upload accepted for background processing',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', description: 'Product summary ID' },
        status: { type: 'string', enum: ['processing'], description: 'Processing status' },
        message: { type: 'string', description: 'Status message' },
        estimatedCompletionTime: { type: 'string', description: 'Estimated completion time' }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid file or parameters',
    type: PdfParsingErrorDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async uploadAndParsePdfBackground(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadPdfDto: UploadPdfDto,
    @Request() req: any,
  ): Promise<{
    id: number;
    status: string;
    message: string;
    estimatedCompletionTime: string;
  }> {
    return await this.pdfParsingService.startBackgroundPdfProcessing(
      file,
      uploadPdfDto,
      req.user.id,
    );
  }

  @Get('summary/:id')
  @ApiOperation({
    summary: 'Get product summary by ID',
    description: 'Retrieve a specific product summary by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Product summary ID',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Product summary retrieved successfully',
    type: ProductSummaryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Product summary not found',
  })
  async getProductSummary(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<ProductSummaryResponseDto> {
    return await this.pdfParsingService.getProductSummary(id, req.user.id);
  }
  @Get('summaries')
  @ApiOperation({
    summary: 'Get all product summaries for current user enterprise',
    description:
      'Retrieve paginated list of product summaries for the authenticated user enterprise',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (starting from 1)',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (max 50)',
    required: false,
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Product summaries retrieved successfully',
    type: ProductSummaryListResponseDto,
  })
  async getCurrentUserEnterpriseSummaries(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<ProductSummaryListResponseDto> {
    // Set defaults for optional parameters
    const currentPage = page || 1;
    const itemsPerPage = limit || 10;

    // Limit maximum items per page
    const maxLimit = Math.min(itemsPerPage, 50);

    if (!req.user.enterprise?.id) {
      throw new Error('User is not associated with any enterprise');
    }

    return await this.pdfParsingService.getEnterpriseSummaries(
      req.user.enterprise.id,
      currentPage,
      maxLimit,
    );
  }
  @Post('summary/:id/update')
  @Get('health')
  @ApiOperation({
    summary: 'Health check for PDF parsing service',
    description: 'Check if the PDF parsing service is operational',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
  })
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('search')
  @ApiOperation({
    summary: 'Search product summaries by title',
    description:
      'Search for product summaries with titles similar to the query string within the user enterprise',
  })
  @ApiQuery({
    name: 'query',
    description: 'Search query to find similar titles',
    required: true,
    type: 'string',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number (starting from 1)',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (max 20)',
    required: false,
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
    type: SearchSummariesResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid search parameters',
  })
  async searchSimilarSummaries(
    @Request() req: any,
    @Query('query') query: string,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<SearchSummariesResponseDto> {
    // Validate required query parameter
    if (!query || query.trim().length === 0) {
      throw new BadRequestException('Search query is required');
    }

    // Set defaults for optional parameters
    const currentPage = page || 1;
    const itemsPerPage = Math.min(limit || 10, 20);

    return await this.pdfParsingService.searchSimilarSummaries(
      req.user.id,
      query.trim(),
      currentPage,
      itemsPerPage,
    );
  }

  @Post('upload-from-s3')
  @ApiOperation({
    summary: 'Parse PDF document from S3 URL',
    description:
      'Parse a PDF file stored in S3 and generate a structured summary using Amazon Nova Lite model',
  })
  @ApiResponse({
    status: 201,
    description: 'PDF parsed successfully from S3',
    type: PdfParsingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid S3 URL or parameters',
    type: PdfParsingErrorDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: PdfParsingErrorDto,
  })
  async parsePdfFromS3(
    @Body() uploadPdfFromS3Dto: UploadPdfFromS3Dto,
    @Request() req: any,
  ): Promise<PdfParsingResponseDto> {
    return await this.pdfParsingService.parsePdfFromS3AndCreateSummary(
      uploadPdfFromS3Dto,
      req.user.id,
    );
  }

  @Post('upload-from-s3-bucket')
  @ApiOperation({
    summary: 'Parse PDF document from S3 bucket and key',
    description:
      'Parse a PDF file stored in S3 using bucket name and key, and generate a structured summary using Amazon Nova Lite model',
  })
  @ApiResponse({
    status: 201,
    description: 'PDF parsed successfully from S3 bucket',
    type: PdfParsingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid S3 bucket/key or parameters',
    type: PdfParsingErrorDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: PdfParsingErrorDto,
  })
  async parsePdfFromS3BucketKey(
    @Body() uploadPdfFromS3BucketKeyDto: UploadPdfFromS3BucketKeyDto,
    @Request() req: any,
  ): Promise<PdfParsingResponseDto> {
    return await this.pdfParsingService.parsePdfFromS3BucketKeyAndCreateSummary(
      uploadPdfFromS3BucketKeyDto,
      req.user.id,
    );
  }

  @Post('update/:id')
  @ApiOperation({
    summary: 'Update product summary',
    description: 'Update an existing product summary with new data including images and content',
  })
  @ApiParam({
    name: 'id',
    description: 'Product summary ID',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Product summary updated successfully',
    type: ProductSummaryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Product summary not found',
  })
  async updateProductSummary(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateSummaryDto,
    @Request() req: any,
  ): Promise<ProductSummaryResponseDto> {
    return await this.pdfParsingService.updateProductSummary(id, updateDto, req.user.id);
  }

  @Post(':id/reprocess')
  @ApiOperation({
    summary: 'Re-process PDF from stored source',
    description: 'Re-process the original PDF using stored source information (S3 only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Product summary ID',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'PDF re-processed successfully',
    type: PdfParsingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot re-process - no source information available',
  })
  async reprocessProductSummary(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { title?: string; additionalContext?: string },
    @Request() req: any,
  ): Promise<PdfParsingResponseDto> {
    return await this.pdfParsingService.reprocessProductSummary(
      id,
      req.user.id,
      body.title,
      body.additionalContext,
    );
  }

  @Get('status/:id')
  @ApiOperation({
    summary: 'Get processing status of product summary',
    description: 'Check the current processing status of a product summary',
  })
  @ApiParam({
    name: 'id',
    description: 'Product summary ID',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Processing status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        status: { type: 'string', enum: ['processing', 'completed', 'failed'] },
        progress: { type: 'number', minimum: 0, maximum: 100 },
        message: { type: 'string' },
        result: { type: 'object', description: 'Available only when status is completed' },
        error: { type: 'string', description: 'Available only when status is failed' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Product summary not found',
  })
  async getProcessingStatus(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<{
    id: number;
    status: string;
    progress: number;
    message: string;
    result?: any;
    error?: string;
  }> {
    return await this.pdfParsingService.getProcessingStatus(id, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete product summary',
    description: 'Delete a product summary and all associated data',
  })
  @ApiParam({
    name: 'id',
    description: 'Product summary ID to delete',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Product summary deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Product summary not found',
    type: PdfParsingErrorDto,
  })
  @HttpCode(HttpStatus.OK)
  async deleteProductSummary(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<{ success: boolean; message: string }> {
    return await this.pdfParsingService.deleteProductSummary(id, req.user.id);
  }
}
