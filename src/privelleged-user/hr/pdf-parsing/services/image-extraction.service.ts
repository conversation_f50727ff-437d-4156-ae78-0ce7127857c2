import { Injectable, Logger } from '@nestjs/common';
import {
  PDFDocument,
  PDFPage,
  PDFDict,
  PDFName,
  PDFStream,
  PDFRef,
  PDFNumber,
} from 'pdf-lib';
import * as pako from 'pako';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';

const sharp = require('sharp');

export interface ExtractedImage {
  pageNumber: number;
  s3Url: string;
  fileName: string;
  mimeType: string;
  width?: number;
  height?: number;
}

interface ImageInfo {
  index: number;
  format: string;
  width?: number;
  height?: number;
  data: Uint8Array;
}

@Injectable()
export class ImageExtractionService {
  private readonly logger = new Logger(ImageExtractionService.name);

  constructor(private readonly s3Service: S3Service) {}

  /**
   * Extract all images from PDF and upload to S3
   */
  async extractAndUploadImages(
    pdfBuffer: Buffer,
    documentTitle: string,
    enterpriseId: number,
  ): Promise<ExtractedImage[]> {
    const extractedImages: ExtractedImage[] = [];

    try {
      this.logger.log(
        `Starting image extraction for document: ${documentTitle}`,
      );

      // Load PDF document
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      this.logger.log(`Loaded PDF with ${pdfDoc.getPageCount()} pages`);

      // Extract images using enhanced extraction
      const images = await this.extractImagesWithDecompression(pdfDoc);
      this.logger.log(`Found ${images.length} images in the PDF`);

      // Upload each image to S3
      for (const image of images) {
        try {
          const fileName = `${documentTitle.replace(/\s+/g, '_')}_enterprise${enterpriseId}_img${String(image.index + 1).padStart(3, '0')}.${image.format}`;

          const file = {
            buffer: Buffer.from(image.data),
            originalname: fileName,
            mimetype: this.getMimeType(image.format),
            size: image.data.length,
            fieldname: 'file',
            encoding: '7bit',
            stream: null,
            destination: '',
            filename: fileName,
            path: '',
          } as Express.Multer.File;

          const s3Result = await this.s3Service.uploadFile(file);

          extractedImages.push({
            pageNumber: image.index + 1,
            s3Url: s3Result.Location,
            fileName,
            mimeType: this.getMimeType(image.format),
            width: image.width,
            height: image.height,
          });

          this.logger.log(
            `Successfully uploaded image ${image.index + 1} to S3: ${s3Result.Location}`,
          );
        } catch (uploadError) {
          this.logger.error(
            `Failed to upload image ${image.index + 1} to S3:`,
            uploadError,
          );
          // Continue with other images even if one fails
        }
      }

      this.logger.log(
        `Image extraction completed: ${extractedImages.length} images uploaded to S3`,
      );
      return extractedImages;
    } catch (error) {
      this.logger.error(
        'Failed to extract and upload images from PDF:',
        error.stack || error.message,
      );
      throw new Error(`PDF image extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract images with enhanced decompression capabilities
   */
  private async extractImagesWithDecompression(
    pdfDoc: PDFDocument,
  ): Promise<ImageInfo[]> {
    const images: ImageInfo[] = [];
    let imageIndex = 0;

    try {
      const enumeratedIndirectObjects =
        pdfDoc.context.enumerateIndirectObjects();

      for (const [ref, pdfObject] of enumeratedIndirectObjects) {
        try {
          if (pdfObject instanceof PDFStream) {
            const dict = pdfObject.dict;
            const type = dict.lookup(PDFName.of('Type'));
            const subtype = dict.lookup(PDFName.of('Subtype'));

            if (
              type &&
              type.toString() === '/XObject' &&
              subtype &&
              subtype.toString() === '/Image'
            ) {
              const imageData = await this.extractAndDecompressImage(
                pdfObject,
                imageIndex,
              );
              if (imageData) {
                images.push(imageData);
                imageIndex++;
              }
            }
          }
        } catch (error) {
          this.logger.warn(
            `Failed to process PDF object: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }
    } catch (error) {
      throw new Error(
        `Failed to enumerate PDF objects: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }

    return images;
  }

  /**
   * Extract and decompress image from PDF stream
   */
  private async extractAndDecompressImage(
    stream: PDFStream,
    index: number,
  ): Promise<ImageInfo | null> {
    try {
      const dict = stream.dict;

      const filter = dict.lookup(PDFName.of('Filter'));
      const width = dict.lookup(PDFName.of('Width'));
      const height = dict.lookup(PDFName.of('Height'));

      let widthNum: number | undefined;
      let heightNum: number | undefined;

      if (width instanceof PDFNumber) {
        widthNum = width.asNumber();
      }
      if (height instanceof PDFNumber) {
        heightNum = height.asNumber();
      }

      const contents = (stream as any).contents;
      if (!contents || contents.length === 0) {
        return null;
      }

      let format = 'unknown';
      let imageData = contents;
      const filterStr = filter?.toString();

      this.logger.log(
        `Processing image ${index + 1}: Filter=${filterStr}, Size=${widthNum}x${heightNum}, Data=${contents.length} bytes`,
      );

      if (filterStr === '/DCTDecode') {
        format = 'jpg';
        imageData = contents;
      } else if (filterStr === '/FlateDecode') {
        try {
          const decompressed = pako.inflate(contents);
          this.logger.log(
            `Decompressed: ${contents.length} -> ${decompressed.length} bytes`,
          );

          // Check for standard image headers in decompressed data
          if (decompressed.length >= 10) {
            const header = decompressed.slice(0, 10);
            if (header[0] === 0xff && header[1] === 0xd8) {
              format = 'jpg';
              imageData = decompressed;
            } else if (
              header[0] === 0x89 &&
              header[1] === 0x50 &&
              header[2] === 0x4e &&
              header[3] === 0x47
            ) {
              format = 'png';
              imageData = decompressed;
            } else {
              // Raw bitmap data - convert to PNG
              try {
                if (widthNum && heightNum) {
                  const pngData = await this.convertBitmapToPNG(
                    decompressed,
                    widthNum,
                    heightNum,
                  );
                  format = 'png';
                  imageData = pngData;
                  this.logger.log(
                    `Converted bitmap to PNG: ${decompressed.length} -> ${pngData.length} bytes`,
                  );
                } else {
                  format = 'bin';
                  imageData = decompressed;
                  this.logger.log(
                    `Missing dimensions, saved as binary: ${decompressed.length} bytes`,
                  );
                }
              } catch (convError) {
                this.logger.warn(
                  `Bitmap conversion failed: ${convError instanceof Error ? convError.message : 'Unknown error'}`,
                );
                format = 'bin';
                imageData = decompressed;
              }
            }
          } else {
            format = 'bin';
            imageData = decompressed;
          }
        } catch (error) {
          this.logger.warn(
            `Failed to decompress: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          format = 'raw';
          imageData = contents;
        }
      } else if (filterStr === '/CCITTFaxDecode') {
        format = 'tiff';
      } else if (filterStr === '/JPXDecode') {
        format = 'jp2';
      } else {
        // Check raw data for image signatures
        if (contents.length >= 10) {
          const header = contents.slice(0, 10);
          if (header[0] === 0xff && header[1] === 0xd8) {
            format = 'jpg';
          } else if (
            header[0] === 0x89 &&
            header[1] === 0x50 &&
            header[2] === 0x4e &&
            header[3] === 0x47
          ) {
            format = 'png';
          } else {
            format = 'raw';
          }
        } else {
          format = 'raw';
        }
      }

      return {
        index,
        format,
        width: widthNum,
        height: heightNum,
        data: imageData,
      };
    } catch (error) {
      throw new Error(
        `Failed to extract image: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Convert raw bitmap data to PNG format using Sharp
   */
  private async convertBitmapToPNG(
    rawData: Uint8Array,
    width: number,
    height: number,
  ): Promise<Uint8Array> {
    try {
      const analysis = this.analyzeBitmapFormat(rawData, width, height);

      let processedData: Buffer;
      let channels: number;

      if (analysis.channels === 1) {
        // Grayscale
        processedData = Buffer.from(rawData.slice(0, width * height));
        channels = 1;
      } else if (analysis.channels === 3) {
        // RGB
        processedData = Buffer.from(rawData.slice(0, width * height * 3));
        channels = 3;
      } else if (analysis.channels === 4) {
        // RGBA or CMYK - convert to RGB
        const rgbData = this.convertToRGB(
          rawData,
          width,
          height,
          analysis.channels,
        );
        processedData = Buffer.from(rgbData);
        channels = 3;
      } else {
        // Unknown format - try as RGB with padding
        const rgbData = this.extractRGBFromRaw(rawData, width, height);
        processedData = Buffer.from(rgbData);
        channels = 3;
      }

      // Create Sharp image from raw data
      const sharpImage = sharp(processedData, {
        raw: {
          width,
          height,
          channels,
        },
      });

      // Convert to PNG
      const pngBuffer = await sharpImage.png().toBuffer();
      return new Uint8Array(pngBuffer);
    } catch (error) {
      throw new Error(
        `Failed to convert bitmap to PNG: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Extract RGB data from raw bitmap with potential padding
   */
  private extractRGBFromRaw(
    rawData: Uint8Array,
    width: number,
    height: number,
  ): Uint8Array {
    const rgbData = new Uint8Array(width * height * 3);
    const bytesPerPixel = rawData.length / (width * height);

    for (let i = 0; i < width * height; i++) {
      const srcIndex = Math.floor(i * bytesPerPixel);
      const dstIndex = i * 3;

      // Copy RGB values, handling different byte arrangements
      rgbData[dstIndex] = rawData[srcIndex]; // R
      rgbData[dstIndex + 1] = rawData[srcIndex + 1] || 0; // G
      rgbData[dstIndex + 2] = rawData[srcIndex + 2] || 0; // B
    }

    return rgbData;
  }

  /**
   * Convert 4-channel data to RGB (handles both RGBA and CMYK)
   */
  private convertToRGB(
    data: Uint8Array,
    width: number,
    height: number,
    channels: number,
  ): Uint8Array {
    const pixelCount = width * height;
    const rgbData = new Uint8Array(pixelCount * 3);

    for (let i = 0; i < pixelCount; i++) {
      const srcIndex = i * channels;
      const dstIndex = i * 3;

      if (channels === 4) {
        // Try RGBA first
        rgbData[dstIndex] = data[srcIndex]; // R
        rgbData[dstIndex + 1] = data[srcIndex + 1]; // G
        rgbData[dstIndex + 2] = data[srcIndex + 2]; // B
        // Skip alpha channel
      }
    }

    return rgbData;
  }

  /**
   * Analyze raw bitmap data to determine likely format
   */
  private analyzeBitmapFormat(
    rawData: Uint8Array,
    width: number,
    height: number,
  ): {
    likelyFormat: string;
    channels: number;
    bytesPerPixel: number;
  } {
    const totalPixels = width * height;
    const bytesPerPixel = rawData.length / totalPixels;

    let likelyFormat: string;
    let channels: number;

    if (Math.abs(bytesPerPixel - 1) < 0.1) {
      likelyFormat = 'Grayscale';
      channels = 1;
    } else if (Math.abs(bytesPerPixel - 3) < 0.1) {
      likelyFormat = 'RGB';
      channels = 3;
    } else if (Math.abs(bytesPerPixel - 4) < 0.1) {
      likelyFormat = 'RGBA or CMYK';
      channels = 4;
    } else {
      likelyFormat = 'Unknown/Custom';
      channels = Math.round(bytesPerPixel);
    }

    return {
      likelyFormat,
      channels,
      bytesPerPixel,
    };
  }

  /**
   * Get MIME type for file format
   */
  private getMimeType(format: string): string {
    switch (format.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'tiff':
        return 'image/tiff';
      case 'jp2':
        return 'image/jp2';
      default:
        return 'application/octet-stream';
    }
  }
}
