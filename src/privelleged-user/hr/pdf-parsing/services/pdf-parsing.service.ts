import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductSummaryEntity } from 'src/models/pdf-parsing-entity/parser.entity';
import { UserEntity } from 'src/models/user-entity';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { NovaAiService, PdfParsingResultUtil } from './nova-ai.service';
import {
  ImageExtractionService,
  ExtractedImage,
} from './image-extraction.service';
import {
  UploadPdfDto,
  UploadPdfFromS3Dto,
  UploadPdfFromS3BucketKeyDto,
  PdfParsingResponseDto,
  ProductSummaryResponseDto,
  ProductSummaryListResponseDto,
  UpdateSummaryDto,
  SearchSummariesDto,
  SearchSummariesResponseDto,
} from '../dto';

@Injectable()
export class PdfParsingService {
  private readonly logger = new Logger(PdfParsingService.name);
  constructor(
    @InjectRepository(ProductSummaryEntity)
    private readonly productSummaryRepository: Repository<ProductSummaryEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly novaAiService: NovaAiService,
    private readonly imageExtractionService: ImageExtractionService,
    private readonly s3Service: S3Service,
  ) {}

  // Background processing tracking
  private processingJobs = new Map<number, {
    status: 'processing' | 'completed' | 'failed';
    progress: number;
    message: string;
    result?: any;
    error?: string;
    startTime: number;
  }>();

  /**
   * Parse PDF and create product summary
   */ async parsePdfAndCreateSummary(
    file: Express.Multer.File,
    uploadPdfDto: UploadPdfDto,
    userId: number,
  ): Promise<PdfParsingResponseDto> {
    const startTime = Date.now();

    try {
      // Get user and validate enterprise
      const user = await this.validateUserHasEnterprise(userId);

      // Validate PDF file
      this.novaAiService.validatePdfFile(file);

      this.logger.log(
        `Starting PDF parsing for user ${userId}, enterprise ${user.enterprise.id}, file: ${file.originalname} (${file.size} bytes)`,
      );

      // Upload PDF to S3 first
      const { s3Url, s3Key } = await this.uploadPdfToS3(
        file,
        user.enterprise.id,
        uploadPdfDto.title,
      );

      // Parse PDF using Nova AI
      const parsingResult = await this.novaAiService.parsePdfContent(
        file.buffer,
        uploadPdfDto.title,
        uploadPdfDto.additionalContext,
      );

      // Extract actual images from PDF and upload to S3
      let extractedImages: ExtractedImage[] = [];
      try {
        this.logger.log('Starting image extraction and S3 upload...');
        extractedImages =
          await this.imageExtractionService.extractAndUploadImages(
            file.buffer,
            uploadPdfDto.title,
            user.enterprise.id,
          );
        this.logger.log(
          `Successfully extracted and uploaded ${extractedImages.length} images to S3`,
        );
      } catch (error) {
        this.logger.warn(
          `Image extraction failed, continuing without images: ${error.message}`,
        );
        // Continue processing even if image extraction fails
      }

      // Merge extracted image URLs with Nova's image analysis
      const enhancedImageAnalyses = this.mergeImageAnalysisWithExtractedImages(
        parsingResult.imageAnalyses,
        extractedImages,
      );

      // Update parsing result with extracted images
      parsingResult.imageAnalyses = enhancedImageAnalyses;
      parsingResult.extractedImageUrls = extractedImages.map(
        (img) => img.s3Url,
      );
      if (parsingResult.metadata) {
        parsingResult.metadata.totalExtractedImages = extractedImages.length;
      } // Extract ALL content (text + images) as clean plain text for storage
      const newContent =
        parsingResult.fullTextContent ||
        PdfParsingResultUtil.extractAllContentAsText(parsingResult);

      // Log parsing quality metrics
      this.logger.log(
        `Nova parsing completed - Content length: ${newContent?.length || 0}, ` +
          `Sections: ${parsingResult.structuredContent?.length || 0}, ` +
          `Images: ${parsingResult.imageAnalyses?.length || 0}, ` +
          `Processing time: ${parsingResult.processingTime}ms`,
      );

      // Validate parsing result quality
      this.validateParsingResult(parsingResult); 
      
      // Prepare source information for tracking
      const sourceInfo = {
        sourceType: 's3_url',
        sourceLocation: s3Url,
        originalFileName: file.originalname,
        fileSizeBytes: file.size,
        totalPages: parsingResult.metadata?.totalPages,
      };

      // Check if title already exists and merge content intelligently
      const productSummary = await this.createOrUpdateProductSummary(
        uploadPdfDto,
        userId,
        newContent,
        parsingResult,
        user,
        sourceInfo,
      );
      const totalProcessingTime = Date.now() - startTime;

      this.logger.log(
        `PDF parsing completed successfully. Summary ID: ${productSummary.id}, Processing time: ${totalProcessingTime}ms`,
      );
      return {
        success: true,
        message: 'PDF parsed and comprehensive analysis generated successfully',
        productSummaryId: productSummary.id,
        fullTextContent: parsingResult.fullTextContent || newContent,
        structuredContent: parsingResult.structuredContent,
        imageAnalyses: parsingResult.imageAnalyses,
        extractedImageUrls: parsingResult.extractedImageUrls || [],
        summary: parsingResult.summary,
        processingTime: totalProcessingTime,
        metadata: parsingResult.metadata,
      };
    } catch (error) {
      this.logger.error(
        `PDF parsing failed for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  /**
   * Get product summary by ID
   */ async getProductSummary(
    id: number,
    userId: number,
  ): Promise<ProductSummaryResponseDto> {
    try {
      // Get user to validate they have enterprise access
      const user = await this.validateUserHasEnterprise(userId);

      const summary = await this.productSummaryRepository
        .createQueryBuilder('summary')
        .leftJoinAndSelect('summary.uploadedBy', 'user')
        .where('summary.id = :id', { id })
        .andWhere('summary.isDeleted = :isDeleted', { isDeleted: false })
        .andWhere('summary.enterpriseId = :enterpriseId', {
          enterpriseId: user.enterprise.id,
        })
        .getOne();
      if (!summary) {
        throw new NotFoundException(
          'Product summary not found or access denied',
        );
      }

      // Verify user has access (either they uploaded it or same enterprise)
      if (
        summary.uploadedByUserId !== userId &&
        summary.enterpriseId !== user.enterprise.id
      ) {
        throw new NotFoundException(
          'Product summary not found or access denied',
        );
      }

      return this.transformEntityToResponseDto(summary);
    } catch (error) {
      this.logger.error(
        `Failed to get product summary ${id}: ${error.message}`,
      );
      throw error;
    }
  }
  /**
   * Get all product summaries for an enterprise
   */
  async getEnterpriseSummaries(
    enterpriseId: number,
    page: number = 1,
    limit: number = 10,
  ): Promise<ProductSummaryListResponseDto> {
    try {
      const [summaries, total] = await this.productSummaryRepository
        .createQueryBuilder('summary')
        .leftJoinAndSelect('summary.uploadedBy', 'user')
        .where('summary.enterpriseId = :enterpriseId', { enterpriseId })
        .andWhere('summary.isDeleted = :isDeleted', { isDeleted: false })
        .orderBy('summary.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        summaries: summaries.map((summary) =>
          this.transformEntityToResponseDto(summary),
        ),
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get enterprise summaries for enterprise ${enterpriseId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Update existing product summary
   */
  async updateProductSummary(
    id: number,
    updateDto: UpdateSummaryDto,
    userId: number,
  ): Promise<ProductSummaryResponseDto> {
    try {
      // Get the existing record
      const existingRecord = await this.productSummaryRepository.findOne({
        where: { id, isDeleted: false },
        relations: ['uploadedBy'],
      });

      if (!existingRecord) {
        throw new NotFoundException('Product summary not found');
      }

      // Validate user has access (same enterprise)
      const user = await this.validateUserHasEnterprise(userId);
      if (existingRecord.enterpriseId !== user.enterprise.id) {
        throw new NotFoundException('Product summary not found');
      }

      // Update fields
      if (updateDto.title) existingRecord.title = updateDto.title;
      if (updateDto.summary) existingRecord.summary = updateDto.summary;
      if (updateDto.content) existingRecord.content = updateDto.content;

      if (updateDto.structuredContent) {
        existingRecord.structuredContentJson = JSON.stringify(updateDto.structuredContent);
      }

      if (updateDto.imageAnalyses) {
        existingRecord.imageAnalysesJson = JSON.stringify(updateDto.imageAnalyses);
      }

      if (updateDto.extractedImageUrls) {
        existingRecord.extractedImageUrls = JSON.stringify(updateDto.extractedImageUrls);
      }

      existingRecord.updatedAt = new Date();

      // Save updated record
      const savedRecord = await this.productSummaryRepository.save(existingRecord);

      this.logger.log(`Updated product summary ${id} by user ${userId}`);

      // Return the updated record
      return this.transformEntityToResponseDto(savedRecord);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update product summary ${id}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to update product summary');
    }
  }

  /**
   * Re-process PDF from stored source
   */
  async reprocessProductSummary(
    id: number,
    userId: number,
    newTitle?: string,
    additionalContext?: string,
  ): Promise<PdfParsingResponseDto> {
    try {
      // Get the existing record
      const existingRecord = await this.productSummaryRepository.findOne({
        where: { id, isDeleted: false },
      });

      if (!existingRecord) {
        throw new NotFoundException('Product summary not found');
      }

      // Validate user has access
      const user = await this.validateUserHasEnterprise(userId);
      if (existingRecord.enterpriseId !== user.enterprise.id) {
        throw new NotFoundException('Product summary not found');
      }

      if (!existingRecord.sourceType || !existingRecord.sourceLocation) {
        throw new BadRequestException('Cannot re-process: No source information available');
      }

      this.logger.log(`Re-processing PDF for summary ${id}, source: ${existingRecord.sourceType}`);

      const title = newTitle || existingRecord.title;

      // Re-process based on source type
      let parsingResult;
      if (existingRecord.sourceType === 's3_url' || existingRecord.sourceType === 's3') {
        // Handle S3 URL - either legacy 's3' or new 's3_url' format
        parsingResult = await this.novaAiService.parsePdfContentFromS3(
          existingRecord.sourceLocation,
          title,
          additionalContext,
        );
      } else if (existingRecord.sourceType === 's3_bucket') {
        const bucketInfo = JSON.parse(existingRecord.sourceLocation);
        parsingResult = await this.novaAiService.parsePdfContentFromS3BucketKey(
          bucketInfo.bucketName,
          bucketInfo.key,
          title,
          additionalContext,
        );
      } else {
        throw new BadRequestException(
          `Cannot re-process files with source type: ${existingRecord.sourceType}. ` +
          `Only S3-sourced files can be reprocessed. Upload source types such as 'upload' ` +
          `require re-uploading the file to enable reprocessing.`
        );
      }

      // Update the existing record with new parsing results
      const newContent = parsingResult.fullTextContent || 
        PdfParsingResultUtil.extractAllContentAsText(parsingResult);

      existingRecord.title = title;
      existingRecord.content = newContent;
      existingRecord.summary = parsingResult.summary;
      existingRecord.structuredContentJson = JSON.stringify(parsingResult.structuredContent || []);
      existingRecord.imageAnalysesJson = JSON.stringify(parsingResult.imageAnalyses || []);
      existingRecord.extractedImageUrls = JSON.stringify(parsingResult.extractedImageUrls || []);
      existingRecord.updatedAt = new Date();

      await this.productSummaryRepository.save(existingRecord);

      this.logger.log(`Successfully re-processed PDF for summary ${id}`);

      return {
        success: true,
        message: 'PDF re-processed successfully',
        productSummaryId: existingRecord.id,
        fullTextContent: parsingResult.fullTextContent || newContent,
        structuredContent: parsingResult.structuredContent,
        imageAnalyses: parsingResult.imageAnalyses,
        extractedImageUrls: parsingResult.extractedImageUrls || [],
        summary: parsingResult.summary,
        processingTime: parsingResult.processingTime,
        metadata: parsingResult.metadata,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(
        `Failed to re-process PDF for summary ${id}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to re-process PDF');
    }
  }

  /**
   * Debug method to get user's enterprise information
   */
  async getUserEnterpriseInfo(userId: number) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['enterprise'],
        select: ['id', 'email', 'enterprise'],
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return {
        userId: user.id,
        userEmail: user.email,
        enterpriseId: user.enterprise?.id || null,
        enterpriseName: user.enterprise?.name || null,
        message: user.enterprise
          ? `User belongs to enterprise ${user.enterprise.id}`
          : 'User is not associated with any enterprise',
      };
    } catch (error) {
      this.logger.error(
        `Failed to get user enterprise info for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Private helper methods
   */ private async validateUserAndEnterprise(
    userId: number,
    enterpriseId: number,
  ): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['enterprise'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Enhanced error messages for debugging - log types as well
    this.logger.log(
      `User ${userId} enterprise: ${user.enterprise?.id || 'null'} (type: ${typeof user.enterprise?.id}), ` +
        `requested enterprise: ${enterpriseId} (type: ${typeof enterpriseId})`,
    );

    if (!user.enterprise) {
      throw new BadRequestException(
        `User ${userId} is not associated with any enterprise`,
      );
    }

    // Convert both to numbers for safe comparison
    const userEnterpriseId = Number(user.enterprise.id);
    const requestedEnterpriseId = Number(enterpriseId);

    if (userEnterpriseId !== requestedEnterpriseId) {
      throw new BadRequestException(
        `User ${userId} belongs to enterprise ${userEnterpriseId}, but tried to access enterprise ${requestedEnterpriseId}`,
      );
    }
    this.logger.log(
      `Enterprise validation successful for user ${userId} and enterprise ${requestedEnterpriseId}`,
    );
    return user;
  }

  /**
   * Validate user exists and has an enterprise
   */
  private async validateUserHasEnterprise(userId: number): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['enterprise'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.enterprise) {
      throw new BadRequestException(
        `User ${userId} is not associated with any enterprise`,
      );
    }

    this.logger.log(
      `User ${userId} validated with enterprise ${user.enterprise.id}`,
    );
    return user;
  }

  /**
   * Validate parsing result quality
   */
  private validateParsingResult(parsingResult: any): void {
    const fullTextContent =
      PdfParsingResultUtil.extractAllContentAsText(parsingResult);

    // Check if we have meaningful content
    if (!fullTextContent || fullTextContent.trim().length < 10) {
      this.logger.warn('Warning: Very short text content extracted from PDF');
    }

    // Check for potential duplicate content (common sign of model issues)
    const textLength = fullTextContent?.length || 0;
    const uniqueWords = new Set(fullTextContent?.split(/\s+/) || []).size;
    const wordsToUniqueRatio =
      textLength > 0 ? uniqueWords / (textLength / 5) : 0;

    if (wordsToUniqueRatio < 0.3) {
      this.logger.warn(
        'Warning: Detected potential duplicate content in parsing result',
      );
    }

    // Log content quality metrics
    this.logger.debug(
      `Content quality - Text chars: ${textLength}, Unique words: ${uniqueWords}, ` +
        `Sections: ${parsingResult.structuredContent?.length || 0}, Images: ${parsingResult.imageAnalyses?.length || 0}`,
    );
  }

  /**
   * Format structured content for clean storage in database
   */
  private formatContentForStorage(structuredContent: any[]): string {
    if (!structuredContent || !Array.isArray(structuredContent)) {
      return '';
    }

    return structuredContent
      .map((section) => {
        let formatted = '';
        if (section.heading) {
          formatted += `## ${section.heading}\n\n`;
        }
        formatted += section.content.trim();
        if (section.pageNumber) {
          formatted += ` (Page ${section.pageNumber})`;
        }
        return formatted;
      })
      .join('\n\n---\n\n');
  }

  /**
   * Extract unique content by comparing with existing content
   */
  private extractUniqueContent(
    newContent: string,
    existingContent: string,
  ): string {
    if (!existingContent || existingContent.trim().length === 0) {
      return newContent;
    }

    // Split content into chunks (sentences)
    const newSentences = this.splitIntoSentences(newContent);
    const existingSentences = this.splitIntoSentences(existingContent);

    // Convert to normalized form for comparison
    const existingNormalized = new Set(
      existingSentences.map((sentence) => this.normalizeSentence(sentence)),
    );

    // Filter out sentences that already exist
    const uniqueSentences = newSentences.filter((sentence) => {
      const normalized = this.normalizeSentence(sentence);
      return !existingNormalized.has(normalized);
    });

    return uniqueSentences.join(' ').trim();
  }

  /**
   * Split text into sentences for duplicate detection
   */
  private splitIntoSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map((s) => s.trim())
      .filter((s) => s.length > 10);
  }

  /**
   * Normalize sentence for comparison (remove extra spaces, lowercase, etc.)
   */
  private normalizeSentence(sentence: string): string {
    return sentence
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .trim();
  }

  /**
   * Generate comprehensive summary from content
   */
  private generateComprehensiveSummary(
    content: string,
    imageAnalyses: any[] = [],
  ): string {
    const sentences = this.splitIntoSentences(content);
    const maxSummaryLength = 500;

    // Take key sentences up to the length limit
    let summary = '';
    for (const sentence of sentences.slice(0, 5)) {
      // First 5 sentences
      if (summary.length + sentence.length > maxSummaryLength) break;
      summary += sentence + '. ';
    }

    // Add image information if available
    if (imageAnalyses && imageAnalyses.length > 0) {
      summary += ` This document contains ${imageAnalyses.length} visual element(s) including charts, diagrams, or images.`;
    }

    return summary.trim();
  }
  /**
   * Create new product summary or update existing one with smart merging
   */ private async createOrUpdateProductSummary(
    uploadPdfDto: UploadPdfDto | any,
    userId: number,
    newContent: string,
    parsingResult: any,
    user: UserEntity,
    sourceInfo?: {
      sourceType: string;
      sourceLocation: string;
      originalFileName: string;
      fileSizeBytes?: number;
      totalPages?: number;
    },
  ): Promise<ProductSummaryEntity> {
    try {
      const existingRecord = await this.productSummaryRepository.findOne({
        where: {
          title: uploadPdfDto.title,
          enterpriseId: user.enterprise.id,
          isDeleted: false,
        },
      });

      if (existingRecord) {
        this.logger.log(
          `Found existing record for title "${uploadPdfDto.title}". Merging content...`,
        );

        // Merge content intelligently, avoiding duplicates
        const mergedContent = PdfParsingResultUtil.mergeContent(
          existingRecord.content,
          newContent,
        );
        if (mergedContent === existingRecord.content) {
          this.logger.log('No new content found. Returning existing record.');
          return existingRecord;
        }

        // Generate updated comprehensive summary
        const updatedSummary = this.generateComprehensiveSummary(mergedContent);
        existingRecord.content = mergedContent;
        existingRecord.summary = updatedSummary;
        existingRecord.updatedAt = new Date();

        // Update source information if provided
        if (sourceInfo) {
          existingRecord.sourceType = sourceInfo.sourceType;
          existingRecord.sourceLocation = sourceInfo.sourceLocation;
          existingRecord.originalFileName = sourceInfo.originalFileName;
          existingRecord.fileSizeBytes = sourceInfo.fileSizeBytes;
          existingRecord.totalPages = sourceInfo.totalPages;
        }

        // Store extracted image URLs if available
        if (
          parsingResult.extractedImageUrls &&
          parsingResult.extractedImageUrls.length > 0
        ) {
          existingRecord.extractedImageUrls = JSON.stringify(
            parsingResult.extractedImageUrls,
          );
        }

        // Store structured content and image analyses if available
        if (parsingResult.structuredContent) {
          existingRecord.structuredContentJson = JSON.stringify(
            parsingResult.structuredContent,
          );
        }
        if (parsingResult.imageAnalyses) {
          existingRecord.imageAnalysesJson = JSON.stringify(
            parsingResult.imageAnalyses,
          );
        }

        const savedRecord =
          await this.productSummaryRepository.save(existingRecord);

        this.logger.log(
          `Updated existing record ${savedRecord.id}. Content length: ${mergedContent.length} characters.`,
        );

        return savedRecord;
      } else {
        // Create new record
        this.logger.log(
          `Creating new record for title "${uploadPdfDto.title}"`,
        );

        const comprehensiveSummary =
          this.generateComprehensiveSummary(newContent);
        const newRecord = this.productSummaryRepository.create({
          title: uploadPdfDto.title,
          content: newContent,
          summary: comprehensiveSummary,
          enterpriseId: user.enterprise.id,
          uploadedByUserId: userId,
          // Source tracking information
          sourceType: sourceInfo?.sourceType,
          sourceLocation: sourceInfo?.sourceLocation,
          originalFileName: sourceInfo?.originalFileName,
          fileSizeBytes: sourceInfo?.fileSizeBytes,
          totalPages: sourceInfo?.totalPages,
          // Store extracted image URLs if available
          extractedImageUrls:
            parsingResult.extractedImageUrls &&
            parsingResult.extractedImageUrls.length > 0
              ? JSON.stringify(parsingResult.extractedImageUrls)
              : null,
          // Store structured content and image analyses if available
          structuredContentJson: parsingResult.structuredContent
            ? JSON.stringify(parsingResult.structuredContent)
            : null,
          imageAnalysesJson: parsingResult.imageAnalyses
            ? JSON.stringify(parsingResult.imageAnalyses)
            : null,
        });

        const savedRecord = await this.productSummaryRepository.save(newRecord);

        this.logger.log(`Created new record ${savedRecord.id}`);

        return savedRecord;
      }
    } catch (error) {
      this.logger.error(
        `Failed to create/update product summary: ${error.message}`,
      );
      throw new BadRequestException('Failed to save product summary');
    }
  }

  /**
   * Merge Nova's image analysis with actual extracted images from S3
   */
  private mergeImageAnalysisWithExtractedImages(
    imageAnalyses: any[],
    extractedImages: ExtractedImage[],
  ): any[] {
    // Create a map of page numbers to extracted images for quick lookup
    const extractedImagesByPage = new Map<number, ExtractedImage>();
    extractedImages.forEach((img) => {
      extractedImagesByPage.set(img.pageNumber, img);
    });

    // Enhance image analyses with S3 URLs where available
    const enhancedAnalyses = imageAnalyses.map((analysis, index) => {
      const pageNum = analysis.pageLocation;
      const extractedImage = pageNum
        ? extractedImagesByPage.get(pageNum)
        : null;
      if (extractedImage) {
        return {
          ...analysis,
          imageIndex:
            analysis.imageIndex !== undefined ? analysis.imageIndex : index,
          s3Url: extractedImage.s3Url,
          extractedImageFileName: extractedImage.fileName,
          extractedImageType: extractedImage.mimeType,
        };
      }
      return {
        ...analysis,
        imageIndex:
          analysis.imageIndex !== undefined ? analysis.imageIndex : index,
      };
    });

    // Add any extracted images that don't have corresponding Nova analysis
    const analysisPageNumbers = new Set(
      imageAnalyses
        .map((a) => a.pageLocation)
        .filter((page) => page !== undefined && page !== null),
    );

    let nextImageIndex = enhancedAnalyses.length;
    extractedImages.forEach((extractedImg) => {
      if (!analysisPageNumbers.has(extractedImg.pageNumber)) {
        // Create a basic analysis for images without Nova description
        enhancedAnalyses.push({
          imageIndex: nextImageIndex,
          description: `Image extracted from page ${extractedImg.pageNumber} - ${extractedImg.fileName}`,
          associatedTopics: ['visual content', 'extracted image'],
          pageLocation: extractedImg.pageNumber,
          contextualRelevance: 'Visual content from document page',
          s3Url: extractedImg.s3Url,
          extractedImageFileName: extractedImg.fileName,
          extractedImageType: extractedImg.mimeType,
        });
        nextImageIndex++;
      }
    });

    return enhancedAnalyses;
  }

  /**
   * Transform ProductSummaryEntity to ProductSummaryResponseDto with parsed JSON fields
   */
  transformEntityToResponseDto(
    entity: ProductSummaryEntity,
  ): ProductSummaryResponseDto {
    const response: ProductSummaryResponseDto = {
      id: entity.id,
      title: entity.title,
      content: entity.content,
      summary: entity.summary,
      enterpriseId: entity.enterpriseId,
      uploadedByUserId: entity.uploadedByUserId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };

    // Parse image analyses if available
    if (entity.imageAnalysesJson) {
      try {
        response.imageAnalyses = JSON.parse(entity.imageAnalysesJson);
      } catch (error) {
        this.logger.warn(
          `Failed to parse image analyses JSON for entity ${entity.id}: ${error.message}`,
        );
      }
    }

    // Parse extracted image URLs if available
    if (entity.extractedImageUrls) {
      try {
        response.extractedImageUrls = JSON.parse(entity.extractedImageUrls);
      } catch (error) {
        this.logger.warn(
          `Failed to parse extracted image URLs JSON for entity ${entity.id}: ${error.message}`,
        );
      }
    }

    // Include user information if available
    if (entity.uploadedBy) {
      response.uploadedBy = {
        id: entity.uploadedBy.id,
        email: entity.uploadedBy.email,
        firstName: entity.uploadedBy.firstName,
        lastName: entity.uploadedBy.lastName,
      };
    }

    // Include source information for edit functionality
    if (entity.sourceType) {
      response.sourceType = entity.sourceType;
    }
    if (entity.sourceLocation) {
      response.sourceLocation = entity.sourceLocation;
    }
    if (entity.originalFileName) {
      response.originalFileName = entity.originalFileName;
    }
    if (entity.fileSizeBytes) {
      response.fileSizeBytes = entity.fileSizeBytes;
    }
    if (entity.totalPages) {
      response.totalPages = entity.totalPages;
    }

    return response;
  }

  /**
   * Search for product summaries by title similarity
   */
  async searchSimilarSummaries(
    userId: number,
    query: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: {
      id: number;
      title: string;
      summary: string;
      createdAt: Date;
      updatedAt: Date;
    }[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    try {
      // Get user and validate enterprise
      const user = await this.validateUserHasEnterprise(userId);

      // Build search query - using LIKE for case-insensitive partial matching
      const queryBuilder = this.productSummaryRepository
        .createQueryBuilder('summary')
        .where('summary.enterpriseId = :enterpriseId', {
          enterpriseId: user.enterprise.id,
        })
        .andWhere('summary.isDeleted = :isDeleted', { isDeleted: false })
        .andWhere('summary.title LIKE :query', {
          query: `%${query}%`,
        })
        .orderBy('summary.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [results, total] = await queryBuilder.getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      // Transform results to include only necessary fields
      const data = results.map((summary) => ({
        id: summary.id,
        title: summary.title,
        summary: summary.summary,
        createdAt: summary.createdAt,
        updatedAt: summary.updatedAt,
      }));

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to search summaries for user ${userId} with query "${query}": ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Parse PDF from S3 URL and create product summary
   */
  async parsePdfFromS3AndCreateSummary(
    uploadPdfFromS3Dto: UploadPdfFromS3Dto,
    userId: number,
  ): Promise<PdfParsingResponseDto> {
    const startTime = Date.now();

    try {
      // Get user and validate enterprise
      const user = await this.validateUserHasEnterprise(userId);

      // Validate S3 URL
      this.novaAiService.validateS3Url(uploadPdfFromS3Dto.s3Url);

      this.logger.log(
        `Starting PDF parsing from S3 for user ${userId}, enterprise ${user.enterprise.id}, S3 URL: ${uploadPdfFromS3Dto.s3Url}`,
      );

      // Parse PDF using Nova AI from S3
      const parsingResult = await this.novaAiService.parsePdfContentFromS3(
        uploadPdfFromS3Dto.s3Url,
        uploadPdfFromS3Dto.title,
        uploadPdfFromS3Dto.additionalContext,
      );

      // For S3-based parsing, we skip image extraction since we don't have the buffer
      // The parsing result already contains image analysis from Nova AI
      let extractedImages: ExtractedImage[] = [];
      this.logger.log('Skipping image extraction for S3-based parsing');

      // Update parsing result with extracted images (empty for S3)
      parsingResult.extractedImageUrls = [];
      if (parsingResult.metadata) {
        parsingResult.metadata.totalExtractedImages = 0;
      }

      // Extract ALL content (text + images) as clean plain text for storage
      const newContent =
        parsingResult.fullTextContent ||
        PdfParsingResultUtil.extractAllContentAsText(parsingResult);

      // Log parsing quality metrics
      this.logger.log(
        `Nova parsing from S3 completed - Content length: ${newContent?.length || 0}, ` +
          `Sections: ${parsingResult.structuredContent?.length || 0}, ` +
          `Images: ${parsingResult.imageAnalyses?.length || 0}, ` +
          `Processing time: ${parsingResult.processingTime}ms`,
      );

      // Validate parsing result quality
      this.validateParsingResult(parsingResult);

      // Check if title already exists and merge content intelligently
      const productSummary = await this.createOrUpdateProductSummary(
        uploadPdfFromS3Dto,
        userId,
        newContent,
        parsingResult,
        user,
      );

      const totalProcessingTime = Date.now() - startTime;

      this.logger.log(
        `PDF parsing from S3 completed successfully. Summary ID: ${productSummary.id}, Processing time: ${totalProcessingTime}ms`,
      );

      // Return the enhanced parsing response
      return {
        success: true,
        message: 'PDF parsed from S3 and comprehensive analysis generated successfully',
        productSummaryId: productSummary.id,
        fullTextContent: parsingResult.fullTextContent || newContent,
        structuredContent: parsingResult.structuredContent,
        imageAnalyses: parsingResult.imageAnalyses,
        extractedImageUrls: parsingResult.extractedImageUrls || [],
        summary: parsingResult.summary,
        processingTime: totalProcessingTime,
        metadata: parsingResult.metadata,
      };
    } catch (error) {
      this.logger.error(
        `PDF parsing from S3 failed for user ${userId}, S3 URL: ${uploadPdfFromS3Dto.s3Url}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Parse PDF from S3 bucket and key and create product summary
   */
  async parsePdfFromS3BucketKeyAndCreateSummary(
    uploadPdfFromS3BucketKeyDto: UploadPdfFromS3BucketKeyDto,
    userId: number,
  ): Promise<PdfParsingResponseDto> {
    const startTime = Date.now();

    try {
      // Get user and validate enterprise
      const user = await this.validateUserHasEnterprise(userId);

      // Validate S3 bucket and key
      this.novaAiService.validateS3BucketKey(uploadPdfFromS3BucketKeyDto.bucketName, uploadPdfFromS3BucketKeyDto.key);

      this.logger.log(
        `Starting PDF parsing from S3 for user ${userId}, enterprise ${user.enterprise.id}, bucket: ${uploadPdfFromS3BucketKeyDto.bucketName}, key: ${uploadPdfFromS3BucketKeyDto.key}`,
      );

      // Parse PDF using Nova AI from S3
      const parsingResult = await this.novaAiService.parsePdfContentFromS3BucketKey(
        uploadPdfFromS3BucketKeyDto.bucketName,
        uploadPdfFromS3BucketKeyDto.key,
        uploadPdfFromS3BucketKeyDto.title,
        uploadPdfFromS3BucketKeyDto.additionalContext,
      );

      // For S3-based parsing, we skip image extraction since we don't have the buffer
      // The parsing result already contains image analysis from Nova AI
      let extractedImages: ExtractedImage[] = [];
      this.logger.log('Skipping image extraction for S3-based parsing');

      // Update parsing result with extracted images (empty for S3)
      parsingResult.extractedImageUrls = [];
      if (parsingResult.metadata) {
        parsingResult.metadata.totalExtractedImages = 0;
      }

      // Extract ALL content (text + images) as clean plain text for storage
      const newContent =
        parsingResult.fullTextContent ||
        PdfParsingResultUtil.extractAllContentAsText(parsingResult);

      // Log parsing quality metrics
      this.logger.log(
        `Nova parsing from S3 bucket completed - Content length: ${newContent?.length || 0}, ` +
          `Sections: ${parsingResult.structuredContent?.length || 0}, ` +
          `Images: ${parsingResult.imageAnalyses?.length || 0}, ` +
          `Processing time: ${parsingResult.processingTime}ms`,
      );

      // Validate parsing result quality
      this.validateParsingResult(parsingResult);

      // Check if title already exists and merge content intelligently
      const productSummary = await this.createOrUpdateProductSummary(
        uploadPdfFromS3BucketKeyDto,
        userId,
        newContent,
        parsingResult,
        user,
      );

      const totalProcessingTime = Date.now() - startTime;

      this.logger.log(
        `PDF parsing from S3 bucket completed successfully. Summary ID: ${productSummary.id}, Processing time: ${totalProcessingTime}ms`,
      );

      // Return the enhanced parsing response
      return {
        success: true,
        message: 'PDF parsed from S3 bucket and comprehensive analysis generated successfully',
        productSummaryId: productSummary.id,
        fullTextContent: parsingResult.fullTextContent || newContent,
        structuredContent: parsingResult.structuredContent,
        imageAnalyses: parsingResult.imageAnalyses,
        extractedImageUrls: parsingResult.extractedImageUrls || [],
        summary: parsingResult.summary,
        processingTime: totalProcessingTime,
        metadata: parsingResult.metadata,
      };
    } catch (error) {
      this.logger.error(
        `PDF parsing from S3 failed for user ${userId}, bucket: ${uploadPdfFromS3BucketKeyDto.bucketName}, key: ${uploadPdfFromS3BucketKeyDto.key}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Start background PDF processing
   * Returns immediately with a product summary ID and processing status
   */
  async startBackgroundPdfProcessing(
    file: Express.Multer.File,
    uploadPdfDto: UploadPdfDto,
    userId: number,
  ): Promise<{
    id: number;
    status: string;
    message: string;
    estimatedCompletionTime: string;
  }> {
    try {
      // Get user and validate enterprise
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['enterprise'],
      });

      if (!user || !user.enterprise) {
        throw new BadRequestException('User or enterprise not found');
      }

      // Create initial product summary record with "processing" status
      const productSummary = new ProductSummaryEntity();
      productSummary.title = uploadPdfDto.title;
      productSummary.summary = 'Processing...';
      productSummary.content = 'Content is being processed. Please check back in a few minutes.';
      productSummary.uploadedBy = user;
      productSummary.enterpriseId = user.enterprise.id;
      productSummary.isEnableToView = true;
      productSummary.sourceType = 's3_url'; // Will be updated with actual S3 URL during processing
      productSummary.sourceLocation = 'Processing...'; // Placeholder
      productSummary.originalFileName = file.originalname;
      productSummary.fileSizeBytes = file.size;

      const savedSummary = await this.productSummaryRepository.save(productSummary);

      // Initialize background processing status
      this.processingJobs.set(savedSummary.id, {
        status: 'processing',
        progress: 0,
        message: 'Starting PDF processing...',
        startTime: Date.now(),
      });

      // Start background processing (fire and forget)
      this.processInBackground(savedSummary.id, file, uploadPdfDto, userId).catch((error) => {
        this.logger.error(`Background processing failed for ID ${savedSummary.id}:`, error);
        this.processingJobs.set(savedSummary.id, {
          status: 'failed',
          progress: 0,
          message: 'Processing failed',
          error: error.message,
          startTime: Date.now(),
        });
      });

      // Calculate estimated completion time (typically 30-60 seconds for PDF processing)
      const estimatedCompletionTime = new Date(Date.now() + 45000).toISOString(); // 45 seconds

      return {
        id: savedSummary.id,
        status: 'processing',
        message: 'PDF upload successful. Processing in background...',
        estimatedCompletionTime,
      };
    } catch (error) {
      this.logger.error('Error starting background PDF processing:', error);
      throw error;
    }
  }

  /**
   * Get processing status for a product summary
   */
  async getProcessingStatus(
    id: number,
    userId: number,
  ): Promise<{
    id: number;
    status: string;
    progress: number;
    message: string;
    result?: any;
    error?: string;
  }> {
    // Check if product summary exists and belongs to user
    const productSummary = await this.productSummaryRepository.findOne({
      where: { id },
      relations: ['uploadedBy', 'enterprise'],
    });

    if (!productSummary) {
      throw new NotFoundException('Product summary not found');
    }

    if (productSummary.uploadedBy?.id !== userId) {
      throw new BadRequestException('Access denied');
    }

    // Get processing status from memory
    const processingStatus = this.processingJobs.get(id);

    if (!processingStatus) {
      // If no processing status found, assume it's completed
      return {
        id,
        status: 'completed',
        progress: 100,
        message: 'Processing completed',
        result: this.transformEntityToResponseDto(productSummary),
      };
    }

    const response = {
      id,
      status: processingStatus.status,
      progress: processingStatus.progress,
      message: processingStatus.message,
    };

    if (processingStatus.status === 'completed' && processingStatus.result) {
      response['result'] = processingStatus.result;
    }

    if (processingStatus.status === 'failed' && processingStatus.error) {
      response['error'] = processingStatus.error;
    }

    return response;
  }

  /**
   * Background processing method
   */
  private async processInBackground(
    summaryId: number,
    file: Express.Multer.File,
    uploadPdfDto: UploadPdfDto,
    userId: number,
  ): Promise<void> {
    try {
      // Update progress: Starting validation
      this.processingJobs.set(summaryId, {
        status: 'processing',
        progress: 10,
        message: 'Validating PDF file...',
        startTime: Date.now(),
      });

      // Validate PDF file
      this.novaAiService.validatePdfFile(file);

      // Update progress: Uploading PDF to S3
      this.processingJobs.set(summaryId, {
        status: 'processing',
        progress: 15,
        message: 'Uploading PDF to S3...',
        startTime: Date.now(),
      });

      // Get user for enterprise ID
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['enterprise'],
      });

      // Upload PDF to S3
      const { s3Url, s3Key } = await this.uploadPdfToS3(
        file,
        user.enterprise.id,
        uploadPdfDto.title,
      );

      // Update progress: Extracting images
      this.processingJobs.set(summaryId, {
        status: 'processing',
        progress: 20,
        message: 'Extracting images from PDF...',
        startTime: Date.now(),
      });

      // Extract images from PDF
      const extractedImages: ExtractedImage[] = [];
      try {
        const images = await this.imageExtractionService.extractAndUploadImages(
          file.buffer,
          uploadPdfDto.title,
          user.enterprise.id,
        );
        extractedImages.push(...images);
      } catch (imageError) {
        this.logger.warn(`Image extraction failed: ${imageError.message}`);
      }

      // Update progress: Processing with AI
      this.processingJobs.set(summaryId, {
        status: 'processing',
        progress: 40,
        message: 'AI processing PDF content...',
        startTime: Date.now(),
      });

      // Parse PDF with Nova AI
      const parsingResult = await this.novaAiService.parsePdfContent(
        file.buffer,
        uploadPdfDto.title,
        uploadPdfDto.additionalContext,
      );

      // Update progress: Creating summary
      this.processingJobs.set(summaryId, {
        status: 'processing',
        progress: 80,
        message: 'Creating product summary...',
        startTime: Date.now(),
      });

      // Update the product summary with processed data
      const productSummary = await this.productSummaryRepository.findOne({
        where: { id: summaryId },
        relations: ['uploadedBy', 'enterprise'],
      });

      if (productSummary) {
        // Extract all content as clean text
        const allContent = PdfParsingResultUtil.extractAllContentAsText(parsingResult);

        productSummary.content = allContent;
        productSummary.summary = parsingResult.summary;
        productSummary.structuredContentJson = JSON.stringify(parsingResult.structuredContent || []);
        productSummary.imageAnalysesJson = JSON.stringify(parsingResult.imageAnalyses || []);
        productSummary.extractedImageUrls = JSON.stringify(extractedImages.map(img => img.s3Url).filter(Boolean));
        productSummary.totalPages = parsingResult.metadata?.totalPages;
        
        // Update source information with S3 URL
        productSummary.sourceType = 's3_url';
        productSummary.sourceLocation = s3Url;

        await this.productSummaryRepository.save(productSummary);

        // Update progress: Completed
        this.processingJobs.set(summaryId, {
          status: 'completed',
          progress: 100,
          message: 'Processing completed successfully!',
          result: this.transformEntityToResponseDto(productSummary),
          startTime: Date.now(),
        });

        // Clean up after 5 minutes
        setTimeout(() => {
          this.processingJobs.delete(summaryId);
        }, 5 * 60 * 1000);
      }
    } catch (error) {
      this.logger.error(`Background processing failed for ID ${summaryId}:`, error);
      this.processingJobs.set(summaryId, {
        status: 'failed',
        progress: 0,
        message: 'Processing failed',
        error: error.message,
        startTime: Date.now(),
      });

      // Clean up after 5 minutes
      setTimeout(() => {
        this.processingJobs.delete(summaryId);
      }, 5 * 60 * 1000);
    }
  }

  /**
   * Upload PDF file to S3 and return the URL
   */
  private async uploadPdfToS3(
    file: Express.Multer.File,
    enterpriseId: number,
    title: string,
  ): Promise<{ s3Url: string; s3Key: string }> {
    try {
      this.logger.log(`Uploading PDF to S3: ${file.originalname} (${file.size} bytes)`);

      // Create a meaningful file name with enterprise ID and sanitized title
      const sanitizedTitle = title.replace(/[^a-zA-Z0-9-_]/g, '_').substring(0, 50);
      const fileExtension = file.originalname.split('.').pop() || 'pdf';
      const timestamp = Date.now();
      const s3Key = `pdfs/enterprise-${enterpriseId}/${sanitizedTitle}-${timestamp}.${fileExtension}`;

      // Create a new file object with the custom key
      const fileWithCustomKey = {
        ...file,
        originalname: `${sanitizedTitle}-${timestamp}.${fileExtension}`,
      };

      // Upload to S3
      const uploadResult = await this.s3Service.uploadFile(fileWithCustomKey);
      
      const s3Url = uploadResult.Location;
      this.logger.log(`PDF uploaded successfully to S3: ${s3Url}`);

      return { s3Url, s3Key };
    } catch (error) {
      this.logger.error(`Failed to upload PDF to S3: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to upload PDF to S3: ${error.message}`);
    }
  }

  /**
   * Delete product summary
   */
  async deleteProductSummary(
    summaryId: number,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Get user and validate enterprise
      const user = await this.validateUserHasEnterprise(userId);

      // Find the product summary
      const summary = await this.productSummaryRepository.findOne({
        where: {
          id: summaryId,
          enterpriseId: user.enterprise.id,
          isDeleted: false,
        },
      });

      if (!summary) {
        throw new NotFoundException(
          `Product summary with ID ${summaryId} not found`,
        );
      }

      this.logger.log(
        `Deleting product summary ${summaryId} for user ${userId}, enterprise ${user.enterprise.id}`,
      );

      // Soft delete - mark as deleted instead of hard delete
      summary.isDeleted = true;
      summary.updatedAt = new Date();
      
      await this.productSummaryRepository.save(summary);

      // Note: We're not deleting S3 files to maintain data integrity
      // In production, you might want to implement a cleanup job for orphaned S3 files

      this.logger.log(`Successfully deleted product summary ${summaryId}`);

      return {
        success: true,
        message: 'Product summary deleted successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to delete product summary ${summaryId}: ${error.message}`,
        error.stack,
      );
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(
        `Failed to delete product summary: ${error.message}`,
      );
    }
  }
}
