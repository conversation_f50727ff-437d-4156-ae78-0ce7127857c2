import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { PDFDocument } from 'pdf-lib';

export interface ImageAnalysis {
  imageIndex: number;
  description: string;
  associatedTopics: string[];
  pageLocation?: number;
  contextualRelevance: string;
  s3Url?: string; // Added for extracted image URL
  extractedImageFileName?: string; // Added for tracking extracted images
  extractedImageType?: string; // Added for MIME type
}

export interface ContentSection {
  heading?: string;
  content: string;
  pageNumber?: number;
  relatedImages?: number[]; // Indices of related images
}

export interface PdfParsingResult {
  // Full text content from PDF exactly as written
  fullTextContent: string;

  // Structured content extraction (primary storage)
  structuredContent: ContentSection[];

  // Image analysis
  imageAnalyses: ImageAnalysis[];

  // Extracted image URLs from S3
  extractedImageUrls?: string[]; // Array of S3 URLs for actual extracted images

  // Traditional summary (kept for compatibility)
  summary: string;

  // Processing metadata
  processingTime: number;
  metadata?: {
    fileSize: number;
    fileName: string;
    totalPages?: number;
    totalImages?: number;
    totalExtractedImages?: number; // Count of actual images extracted
  };
}

// Utility class for PDF parsing results
export class PdfParsingResultUtil {
  /**
   * Extract ALL content from PDF parsing result as clean plain text
   * This includes text content AND image descriptions as readable text
   */
  static extractAllContentAsText(parsingResult: any): string {
    let allContent = '';

    // 1. Extract text from structured content
    if (
      parsingResult.structuredContent &&
      parsingResult.structuredContent.length > 0
    ) {
      const textContent = parsingResult.structuredContent
        .map((section) => {
          let sectionText = '';

          // Add heading if present
          if (section.heading && section.heading.trim()) {
            sectionText += `${section.heading.trim()}\n\n`;
          }

          // Add content
          if (section.content && section.content.trim()) {
            sectionText += section.content.trim();
          }

          return sectionText;
        })
        .filter((text) => text.length > 0)
        .join('\n\n');

      allContent += textContent;
    }

    // 2. Extract and add image descriptions as text
    if (parsingResult.imageAnalyses && parsingResult.imageAnalyses.length > 0) {
      const imageDescriptions = parsingResult.imageAnalyses
        .map((image, index) => {
          let imageText = `\n\n[IMAGE ${index + 1}]`;
          if (image.description) {
            imageText += `\n${image.description}`;
          }
          if (image.associatedTopics && image.associatedTopics.length > 0) {
            imageText += `\nRelated topics: ${image.associatedTopics.join(', ')}`;
          }
          return imageText;
        })
        .join('\n');

      allContent += imageDescriptions;
    }

    // 3. Clean and return the final content
    return allContent
      .replace(/\t+/g, ' ') // Replace multiple tabs with single space
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n\s*\n+/g, '\n\n') // Replace multiple newlines with double newline
      .replace(/(.)\1{10,}/g, '$1') // Remove excessive character repetition (10+ same chars)
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .join('\n')
      .trim();
  }

  /**
   * Merge new content with existing content, avoiding duplicates
   */
  static mergeContent(existingContent: string, newContent: string): string {
    if (!existingContent) return newContent || '';
    if (!newContent) return existingContent;

    const existingLines = new Set(
      existingContent
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line.length > 0),
    );

    const newLines = newContent
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
    const uniqueNewLines = newLines.filter((line) => !existingLines.has(line));

    if (uniqueNewLines.length === 0) {
      return existingContent; // No new content to add
    }
    return `${existingContent}\n\n${uniqueNewLines.join('\n')}`.trim();
  }
}

@Injectable()
export class NovaAiService {
  private readonly logger = new Logger(NovaAiService.name);
  private bedrockClient: BedrockRuntimeClient;
  private s3Client: S3Client;
  private readonly maxRetries: number;
  private readonly retryDelay: number;
  private readonly bucketName: string;

  constructor(private configService: ConfigService) {
    // Initialize configuration from environment variables
    this.maxRetries = this.configService.get<number>('NOVA_MAX_RETRIES') || 3;
    this.retryDelay =
      this.configService.get<number>('NOVA_RETRY_DELAY') || 1000;
    this.bucketName = this.configService.get<string>('AWS_BUCKET');

    // Initialize AWS Bedrock client for Nova model
    this.bedrockClient = new BedrockRuntimeClient({
      region: this.configService.get<string>('NOVA_REGION') || 'us-east-1',
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });

    // Initialize S3 client for downloading PDFs
    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });
  }
  /**
   * Parse PDF content using Amazon Nova Lite model with enhanced extraction
   * Nova handles all text extraction, OCR, and image analysis natively
   * @param pdfBuffer - PDF file buffer
   * @param title - Document title for context
   * @param additionalContext - Optional context for better parsing
   * @returns Enhanced structured analysis of the PDF content
   */ async parsePdfContent(
    pdfBuffer: Buffer,
    title: string,
    additionalContext?: string,
  ): Promise<PdfParsingResult> {
    const startTime = Date.now();

    try {
      // Get actual page count from PDF
      const pageCount = await this.getPdfPageCount(pdfBuffer);
      this.logger.log(`Processing PDF with Nova AI - ${pageCount} pages`);

      // Build enhanced parsing prompt with page count
      const prompt = this.buildEnhancedParsingPrompt(
        title,
        additionalContext,
        pageCount,
      );

      // Send PDF directly to Nova model for comprehensive parsing
      const rawResponse = await this.callNovaModelWithRetry(prompt, pdfBuffer);

      console.log('rawResponse', rawResponse);

      // Parse the structured response
      const parsedResult = this.parseNovaResponse(rawResponse);

      const processingTime = Date.now() - startTime;

      // Validate that Nova correctly identified page count
      const returnedPageCount = parsedResult.tempMetadata?.totalPages || 1;
      if (returnedPageCount !== pageCount) {
        this.logger.warn(
          `Nova returned incorrect page count: ${returnedPageCount}, actual: ${pageCount}. Correcting metadata.`,
        );
      }

      return {
        ...parsedResult,
        processingTime,
        metadata: {
          fileSize: pdfBuffer.length,
          fileName: `${title}.pdf`,
          totalPages: pageCount, // Use actual page count, not Nova's response
          totalImages: parsedResult.imageAnalyses?.length || 0,
        },
      };
    } catch (error) {
      this.logger.error(`PDF parsing failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException(
        'Failed to parse PDF content. Please try again.',
      );
    }
  }
  /**
   * Build enhanced comprehensive prompt for Nova model with exact extraction requirements
   */
  private buildEnhancedParsingPrompt(
    title: string,
    additionalContext?: string,
    pageCount: number = 1,
  ): string {
    // Get the base prompt from environment variables
    let prompt = this.configService.get<string>('NOVA_EXTRACTION_PROMPT');
    prompt = prompt.replace(/{title}/g, title);
    prompt = prompt.replace(/{pageCount}/g, pageCount.toString());

    if (additionalContext) {
      prompt = prompt.replace('{context}', `\nContext: ${additionalContext}`);
    } else {
      prompt = prompt.replace('{context}', '');
    }

    // Handle escaped newlines from environment variable
    prompt = prompt.replace(/\\n/g, '\n');

    return prompt;
  } /**
   * Parse Nova's structured response into our result format
   */
  private parseNovaResponse(
    rawResponse: string,
  ): Omit<PdfParsingResult, 'processingTime' | 'metadata'> & {
    tempMetadata?: any;
  } {
    // Check if response is empty or just whitespace
    if (!rawResponse || rawResponse.trim().length === 0) {
      this.logger.warn('Nova returned empty response');
      return {
        structuredContent: [],
        imageAnalyses: [],
        summary: 'No content could be extracted from this document.',
        fullTextContent: '',
        tempMetadata: {},
      };
    }

    try {
      // Clean the response - remove any extra text before/after
      let cleanedResponse = rawResponse.trim();

      const jsonStart = cleanedResponse.indexOf('{');
      const jsonEnd = cleanedResponse.lastIndexOf('}');

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        cleanedResponse = cleanedResponse.substring(jsonStart, jsonEnd + 1);
        const parsedJson = JSON.parse(cleanedResponse);
        const result = {
          structuredContent: this.cleanStructuredContent(
            parsedJson.structuredContent || [],
          ),
          imageAnalyses: parsedJson.imageAnalyses || [],
          summary: this.cleanTextFormatting(
            parsedJson.summary || 'No summary available',
          ),
          fullTextContent: this.cleanTextFormatting(
            parsedJson.fullTextContent || '',
          ),
          tempMetadata: parsedJson.metadata,
        };

        const fullTextLength =
          result.fullTextContent?.length ||
          PdfParsingResultUtil.extractAllContentAsText(result).length;

        if (
          fullTextLength > 0 ||
          result.imageAnalyses.length > 0 ||
          result.structuredContent.length > 0
        ) {
          return result;
        }
      }
    } catch (error) {
      this.logger.warn(
        `Failed to parse structured JSON response: ${error.message}`,
      );
    }

    return this.fallbackTextParsing(rawResponse);
  }

  private cleanDuplicateText(text: string): string {
    if (!text) return '';

    let cleanedText = text
      .replace(/\t+/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n+/g, '\n\n')
      .trim();

    // Split by sentences and remove duplicates
    const sentences = cleanedText
      .split(/[.!?]+/)
      .map((s) => s.trim())
      .filter((s) => s.length > 0);
    const uniqueSentences = [...new Set(sentences)];

    return uniqueSentences.join('. ') + (cleanedText.endsWith('.') ? '' : '.');
  }
  private cleanStructuredContent(content: ContentSection[]): ContentSection[] {
    if (!Array.isArray(content)) return [];

    const seen = new Set<string>();
    return content
      .filter((section) => {
        const key = `${section.heading || ''}_${section.content.substring(0, 100)}`;
        if (seen.has(key)) return false;
        seen.add(key);
        return true;
      })
      .map((section) => ({
        ...section,
        heading: this.cleanTextFormatting(section.heading || ''),
        content: this.cleanTextFormatting(section.content),
      }));
  }

  /**
   * Clean text formatting issues including excessive tabs and whitespace
   */
  private cleanTextFormatting(text: string): string {
    if (!text) return '';

    return text
      .replace(/\t+/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n+/g, '\n\n')
      .replace(/(.)\1{10,}/g, '$1')
      .trim();
  } /**
   * Fallback text parsing when structured JSON isn't available
   */
  private fallbackTextParsing(
    rawResponse: string,
  ): Omit<PdfParsingResult, 'processingTime' | 'metadata'> & {
    tempMetadata?: any;
  } {
    this.logger.warn('Using fallback text parsing');

    // Clean the response thoroughly
    let cleanedResponse = this.cleanTextFormatting(rawResponse.trim());

    // Split response into meaningful sections
    const sections = cleanedResponse
      .split(/\n\s*\n+/)
      .filter((section) => section.trim().length > 10);

    const structuredContent: ContentSection[] = [];
    let fullTextContent = '';

    sections.forEach((section, index) => {
      const trimmedSection = section.trim();
      if (!trimmedSection) return;

      // Clean the section before processing
      const cleanedSection = this.cleanTextFormatting(trimmedSection);

      // Look for headings (lines that end with : or are in title case)
      const lines = cleanedSection.split('\n');
      const firstLine = lines[0]?.trim();

      if (
        firstLine &&
        (firstLine.endsWith(':') ||
          (firstLine.length < 100 &&
            firstLine === firstLine.replace(/\b\w/g, (l) => l.toUpperCase())))
      ) {
        structuredContent.push({
          heading: firstLine.replace(':', ''),
          content: lines.slice(1).join('\n').trim(),
          pageNumber: Math.floor(index / 2) + 1,
        });
      } else {
        structuredContent.push({
          content: cleanedSection,
          pageNumber: Math.floor(index / 2) + 1,
        });
      }

      fullTextContent += cleanedSection + '\n\n';
    });
    const summary = this.generateBasicSummary(fullTextContent);

    return {
      structuredContent: this.cleanStructuredContent(structuredContent),
      imageAnalyses: [],
      summary,
      fullTextContent: this.cleanTextFormatting(fullTextContent.trim()),
      tempMetadata: { totalPages: Math.max(1, structuredContent.length) },
    };
  }

  private generateBasicSummary(fullText: string): string {
    if (!fullText || fullText.length < 100) return fullText;

    // Take first few sentences up to a reasonable length
    const sentences = fullText
      .split(/[.!?]+/)
      .filter((s) => s.trim().length > 0);
    let summary = '';

    for (const sentence of sentences) {
      if (summary.length + sentence.length > 300) break;
      summary += sentence.trim() + '. ';
    }

    return summary.trim() || fullText.substring(0, 300) + '...';
  }

  /**
   * Call Nova model with retry logic
   */
  private async callNovaModelWithRetry(
    prompt: string,
    pdfBuffer: Buffer,
  ): Promise<string> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await this.callNovaModel(prompt, pdfBuffer);
      } catch (error) {
        lastError = error;
        this.logger.warn(
          `Nova API call attempt ${attempt} failed: ${error.message}`,
        );

        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt); // Exponential backoff
        }
      }
    }

    throw new InternalServerErrorException(
      `Nova API failed after ${this.maxRetries} attempts: ${lastError.message}`,
    );
  } /**
   * Call Amazon Nova Lite model directly with PDF
   * Nova natively supports PDF parsing including text and images
   */
  private async callNovaModel(
    prompt: string,
    pdfBuffer: Buffer,
  ): Promise<string> {
    try {
      const modelId = this.configService.get<string>('NOVA_MODEL_ID');

      // Convert PDF buffer to base64 for Nova
      const pdfBase64 = pdfBuffer.toString('base64');

      // Optimized payload structure for Nova model
      const payload = {
        messages: [
          {
            role: 'user',
            content: [
              {
                text: prompt,
              },
              {
                document: {
                  format: 'pdf',
                  name: 'document.pdf',
                  source: {
                    bytes: pdfBase64,
                  },
                },
              },
            ],
          },
        ],
        inferenceConfig: {
          maxTokens: this.configService.get<number>('NOVA_MAX_TOKENS') || 6000,
          temperature:
            this.configService.get<number>('NOVA_TEMPERATURE') || 0.1,
          topP: this.configService.get<number>('NOVA_TOP_P') || 0.8,
          stopSequences: ['```', '---'],
        },
      };

      const command = new InvokeModelCommand({
        modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(payload),
      });

      const response = await this.bedrockClient.send(command);

      // Parse the response
      const responseBody = new TextDecoder().decode(response.body);
      const parsedResponse = JSON.parse(responseBody);

      // Extract content from Nova response
      if (parsedResponse.output?.message?.content?.[0]?.text) {
        const content = parsedResponse.output.message.content[0].text;
        return content;
      }

      // Fallback response structure handling
      if (parsedResponse.content?.[0]?.text) {
        const content = parsedResponse.content[0].text;
        return content;
      }

      this.logger.error(
        `Unexpected Nova response format: ${JSON.stringify(parsedResponse)}`,
      );
      throw new Error('Unexpected response format from Nova model');
    } catch (error) {
      this.logger.error(
        `Nova model invocation failed: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Utility method for delays in retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Validate PDF file
   */
  validatePdfFile(file: Express.Multer.File): void {
    const maxSize = this.configService.get<number>('PDF_PARSING_MAX_FILE_SIZE');
    const supportedFormats = this.configService
      .get<string>('PDF_PARSING_SUPPORTED_FORMATS')
      ?.split(',');

    if (!file) {
      throw new BadRequestException('No file provided');
    }

    if (file.size > maxSize) {
      throw new BadRequestException(
        `File size exceeds maximum allowed size of ${Math.round(maxSize / (1024 * 1024))}MB`,
      );
    }

    if (!supportedFormats.includes(file.mimetype)) {
      throw new BadRequestException(
        `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`,
      );
    }

    // Additional PDF validation
    if (!this.isPdfFile(file.buffer)) {
      throw new BadRequestException('Invalid PDF file format');
    }
  }

  /**
   * Check if buffer contains valid PDF content
   */
  private isPdfFile(buffer: Buffer): boolean {
    // Check PDF magic number
    const pdfMagicNumber = Buffer.from([0x25, 0x50, 0x44, 0x46]);
    return buffer.subarray(0, 4).equals(pdfMagicNumber);
  }

  /**
   * Get the actual page count from PDF
   */
  private async getPdfPageCount(pdfBuffer: Buffer): Promise<number> {
    try {
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      return pdfDoc.getPageCount();
    } catch (error) {
      this.logger.warn(
        `Failed to get PDF page count: ${error.message}, defaulting to 1`,
      );
      return 1;
    }
  }

  /**
   * Parse PDF content from S3 URL using Amazon Nova Lite model
   * @param s3Url - S3 URL of the PDF file
   * @param title - Document title for context
   * @param additionalContext - Optional context for better parsing
   * @returns Enhanced structured analysis of the PDF content
   */
  async parsePdfContentFromS3(
    s3Url: string,
    title: string,
    additionalContext?: string,
  ): Promise<PdfParsingResult> {
    const startTime = Date.now();

    try {
      this.logger.log(`Downloading PDF from S3: ${s3Url}`);

      // Download PDF from S3
      const pdfBuffer = await this.downloadPdfFromS3(s3Url);

      this.logger.log(`Downloaded PDF from S3, size: ${pdfBuffer.length} bytes`);

      // Use existing parsing logic with the downloaded buffer
      return await this.parsePdfContent(pdfBuffer, title, additionalContext);
    } catch (error) {
      this.logger.error(
        `Failed to parse PDF from S3 URL ${s3Url}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to parse PDF from S3: ${error.message}`,
      );
    }
  }

  /**
   * Parse PDF content using S3 bucket and key
   * @param bucketName - S3 bucket name
   * @param key - S3 object key
   * @param title - Document title for context
   * @param additionalContext - Optional context for better parsing
   * @returns Enhanced structured analysis of the PDF content
   */
  async parsePdfContentFromS3BucketKey(
    bucketName: string,
    key: string,
    title: string,
    additionalContext?: string,
  ): Promise<PdfParsingResult> {
    try {
      this.logger.log(`Downloading PDF from S3 bucket: ${bucketName}, key: ${key}`);

      // Download PDF from S3 using bucket and key
      const pdfBuffer = await this.downloadPdfFromS3BucketKey(bucketName, key);

      this.logger.log(`Downloaded PDF from S3, size: ${pdfBuffer.length} bytes`);

      // Use existing parsing logic with the downloaded buffer
      return await this.parsePdfContent(pdfBuffer, title, additionalContext);
    } catch (error) {
      this.logger.error(
        `Failed to parse PDF from S3 bucket ${bucketName}, key ${key}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to parse PDF from S3: ${error.message}`,
      );
    }
  }

  /**
   * Download PDF file from S3 URL
   * @param s3Url - S3 URL of the PDF file
   * @returns PDF buffer
   */
  private async downloadPdfFromS3(s3Url: string): Promise<Buffer> {
    try {
      // Parse S3 URL to extract bucket and key
      const { bucketName, key } = this.parseS3Url(s3Url);
      
      return await this.downloadPdfFromS3BucketKey(bucketName, key);
    } catch (error) {
      this.logger.error(`Failed to download PDF from S3 URL ${s3Url}: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to download PDF from S3: ${error.message}`,
      );
    }
  }

  /**
   * Download PDF file from S3 using bucket and key
   * @param bucketName - S3 bucket name
   * @param key - S3 object key
   * @returns PDF buffer
   */
  private async downloadPdfFromS3BucketKey(bucketName: string, key: string): Promise<Buffer> {
    try {
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      const response = await this.s3Client.send(command);

      if (!response.Body) {
        throw new Error('Empty response body from S3');
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      const stream = response.Body as any;

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(
        `Failed to download PDF from S3 bucket ${bucketName}, key ${key}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to download PDF from S3: ${error.message}`,
      );
    }
  }

  /**
   * Parse S3 URL to extract bucket name and key
   * @param s3Url - S3 URL
   * @returns Object with bucketName and key
   */
  private parseS3Url(s3Url: string): { bucketName: string; key: string } {
    try {
      // Handle different S3 URL formats:
      // https://bucket-name.s3.region.amazonaws.com/key
      // https://s3.region.amazonaws.com/bucket-name/key
      // s3://bucket-name/key
      
      if (s3Url.startsWith('s3://')) {
        // s3://bucket-name/key format
        const url = new URL(s3Url);
        const bucketName = url.hostname;
        const key = url.pathname.substring(1); // Remove leading slash
        return { bucketName, key };
      } else if (s3Url.includes('.s3.') || s3Url.includes('s3.')) {
        // HTTPS S3 URL formats
        const url = new URL(s3Url);
        
        if (url.hostname.includes('.s3.')) {
          // Virtual-hosted-style URL: https://bucket-name.s3.region.amazonaws.com/key
          const bucketName = url.hostname.split('.')[0];
          const key = url.pathname.substring(1); // Remove leading slash
          return { bucketName, key };
        } else if (url.hostname.startsWith('s3.')) {
          // Path-style URL: https://s3.region.amazonaws.com/bucket-name/key
          const pathParts = url.pathname.substring(1).split('/'); // Remove leading slash and split
          const bucketName = pathParts[0];
          const key = pathParts.slice(1).join('/');
          return { bucketName, key };
        }
      }
      
      throw new Error(`Unsupported S3 URL format: ${s3Url}`);
    } catch (error) {
      this.logger.error(`Failed to parse S3 URL ${s3Url}: ${error.message}`);
      throw new BadRequestException(`Invalid S3 URL format: ${s3Url}`);
    }
  }

  /**
   * Validate S3 URL format
   */
  validateS3Url(s3Url: string): void {
    if (!s3Url) {
      throw new BadRequestException('No S3 URL provided');
    }

    if (typeof s3Url !== 'string') {
      throw new BadRequestException('S3 URL must be a string');
    }

    // Check if it's a valid S3 URL format
    const s3UrlPattern = /^(s3:\/\/|https:\/\/.*(\.s3\.|s3\.)).*$/;
    if (!s3UrlPattern.test(s3Url)) {
      throw new BadRequestException(
        'Invalid S3 URL format. Supported formats: s3://bucket/key or https://bucket.s3.region.amazonaws.com/key',
      );
    }

    // Try to parse the URL to ensure it's valid
    try {
      this.parseS3Url(s3Url);
    } catch (error) {
      throw new BadRequestException(`Invalid S3 URL: ${error.message}`);
    }
  }

  /**
   * Validate S3 bucket and key
   */
  validateS3BucketKey(bucketName: string, key: string): void {
    if (!bucketName) {
      throw new BadRequestException('No S3 bucket name provided');
    }

    if (!key) {
      throw new BadRequestException('No S3 key provided');
    }

    if (typeof bucketName !== 'string' || typeof key !== 'string') {
      throw new BadRequestException('S3 bucket name and key must be strings');
    }

    // Basic S3 bucket name validation
    const bucketNamePattern = /^[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$/;
    if (!bucketNamePattern.test(bucketName)) {
      throw new BadRequestException(
        'Invalid S3 bucket name format. Bucket names must be 3-63 characters long and contain only lowercase letters, numbers, periods, and hyphens.',
      );
    }

    // Basic S3 key validation - ensure it's not empty and doesn't start with /
    if (key.startsWith('/')) {
      throw new BadRequestException('S3 key should not start with forward slash');
    }

    // Check if key looks like a PDF file
    if (!key.toLowerCase().endsWith('.pdf')) {
      throw new BadRequestException('S3 key must point to a PDF file (.pdf extension required)');
    }
  }
}
