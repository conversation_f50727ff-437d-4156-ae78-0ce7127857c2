import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { UserEntity } from 'src/models/user-entity';
import { QuestEntity, QuestParticipantEntity, PARTICIPANT_STATUS, SUBMISSION_MEDIA_TYPES, QUEST_SCOPE } from 'src/models/quest-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { QuestResponseDto, UserAggregateResponseDto, UserStrengthsWeaknessesDto } from './dto/user-analytics-response.dto';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { QuestCompletionProofMediaEntity } from 'src/models/quest-entity/quest-completion-proof-media.entity';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { UserQuestMetricsEntity } from 'src/models/metrics-entity/user-quest-metrics.entity';
import { ProductQuestEntity } from 'src/models/products-summary-entity/product-quest.entity';
import { ProductQuestSubmissionEntity } from 'src/models/products-summary-entity/product-quest-submission.entity';
import { ProductMCQQuestEntity } from 'src/models/products-summary-entity/product-mcq-quest.entity';

@Injectable()
export class UserAggregateService {
  constructor(
    private readonly logger: CustomLogger,
    
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    
    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,
    
    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,
    
    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,
    
    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,
    
    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionMediaRepo: Repository<QuestCompletionProofMediaEntity>,

    @InjectRepository(UserQuestMetricsEntity)
    private readonly userQuestMetricsRepo: Repository<UserQuestMetricsEntity>,

    @InjectRepository(ProductQuestEntity)
    private readonly productQuestRepo: Repository<ProductQuestEntity>,
    @InjectRepository(ProductQuestSubmissionEntity)
    private readonly productQuestSubmissionRepo: Repository<ProductQuestSubmissionEntity>,
    @InjectRepository(ProductMCQQuestEntity)
    private readonly productMcqQuestRepo: Repository<ProductMCQQuestEntity>,
  ) {}
  
  async getUserAggregateData(userId: number): Promise<UserAggregateResponseDto> {
    const user = await this.userRepo.findOne({
      where: { id: userId, isDeleted: false },
      relations: ['enterprise'],
    });
    
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const participants = await this.participantRepo.find({
      where: { 
        user: { id: userId }, 
        isDeleted: false,
      },
      relations: ['quest', 'quest.enterprise', 'quest.questType', 'questCompletionProof', 'user'], // Added 'user' to relations
      order: {
        updatedAt: 'DESC', 
      },
    });

    const enterpriseQuests = await this.questRepo.find({
      where: {
        enterprise: { id: user.enterprise?.id || 0 }, 
        scope: QUEST_SCOPE.ENTERPRISE,
        isDeleted: false,
        submissionMediaType: In([SUBMISSION_MEDIA_TYPES.TEXT, SUBMISSION_MEDIA_TYPES.MCQ]),
      },
    });

    const userCredits = await this.userCreditsRepo.find({
      where: { user: { id: userId } },
      relations: ['quest'],
    });

    // Fetch Product Hunt quest credits (where quest is null but questType is PRODUCT_QUEST)
    const productHuntCredits = await this.userCreditsRepo.find({
      where: { 
        user: { id: userId },
        quest: null, // Product Hunt quests have null quest field
        questType: { value: 'PRODUCT_QUEST' }
      },
      relations: ['questType'],
    });

    // Deduplicate credits by quest (or product quest) and submission date
    const uniqueCredits = new Map();
    userCredits.forEach(credit => {
      const key = credit.quest
        ? `quest_${credit.quest.id}_${credit.submissionDate}`
        : credit.questType && credit.submissionDate
          ? `product_${credit.questType.id}_${credit.submissionDate}`
          : `unknown_${credit.submissionDate}`;
      if (!uniqueCredits.has(key)) {
        uniqueCredits.set(key, credit.credits);
      }
    });
    const creditScore = Array.from(uniqueCredits.values()).reduce((sum, c) => sum + c, 0);

    const completedParticipants = participants.filter(
      p => p.status === PARTICIPANT_STATUS.COMPLETED
    );
    const completedQuestIds = completedParticipants.map(p => p.quest.id);
    
    const completedCount = completedParticipants.length;
    const totalCount = enterpriseQuests.length;
    
    // Calculate average score including Product Hunt quests
    const standardQuestCredits = userCredits
      .filter(credit => credit.quest && completedQuestIds.includes(credit.quest.id))
      .reduce((sum, credit) => sum + credit.credits, 0);
    
    const productHuntQuestCredits = productHuntCredits.reduce((sum, credit) => sum + credit.credits, 0);
    const totalQuestCredits = standardQuestCredits + productHuntQuestCredits;
    const totalCompletedQuests = completedCount + productHuntCredits.length;
    
    const avgScore = totalCompletedQuests > 0 ? totalQuestCredits / totalCompletedQuests : 0;
    
    const engagementScore = totalCount > 0 ? 
      (completedCount / totalCount) * avgScore : 0;
    
    const knowledgeScore = engagementScore;

    // Fetch completed Product Hunt quest submissions for this user
    const productHuntSubmissions = await this.productQuestSubmissionRepo.find({
      where: { submittedByUserId: { id: userId } },
      relations: ['productQuest', 'productQuest.questType', 'submittedByUserId'],
    });
    // Only include those where the quest is not deleted and isActive
    const completedProductHuntSubmissions = productHuntSubmissions.filter(
      (sub) => sub.productQuest && !sub.productQuest.isDeleted && sub.productQuest.isActive
    );
    // Map to QuestResponseDto
    const productHuntQuestResponses = await Promise.all(
      completedProductHuntSubmissions.map(async (submission) => {
        const quest = submission.productQuest;
        // Fetch MCQ questions if MCQ type
        let mcqQuestions = [];
        if (quest.submissionType === 'MCQ') {
          mcqQuestions = await this.productMcqQuestRepo.find({ where: { questId: quest.id, isDeleted: false }, order: { id: 'ASC' } });
        }
        // Try to fetch metrics for this quest/user
        const questMetrics = await this.userQuestMetricsRepo.findOne({
          where: {
            user: { id: userId },
            productQuest: { id: quest.id },
          },
        });

        // Try to fetch credits for this quest/user
        const questCredits = await this.userCreditsRepo.findOne({
          where: {
            user: { id: userId },
            quest: null, // Product Hunt quests have null quest
            questType: { value: 'PRODUCT_QUEST' },
            // Match by submission date to get the correct credit for this specific submission
            submissionDate: submission.submittedAt ? submission.submittedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
          },
          relations: ['questType'],
          order: { createdAt: 'DESC' }, // Get the most recent credit for this user
        });

        let score = null;
        let totalPossible = null;
        let userResponse: any = null;
        let aiAnalysisScore = null;
        let questEvaluation = null;

        if (quest.submissionType === 'MCQ') {
          // Map user's answers to questions/options and wrap in { answers: [...] }
          userResponse = {
            answers: (submission.mcqAnswers || []).map(ans => {
              const question = mcqQuestions.find(q => q.id === ans.questionId);
              if (!question) return null;
              const isCorrect = Array.isArray(ans.selectedOptions) && Array.isArray(question.correctAnswers) &&
                ans.selectedOptions.length === question.correctAnswers.length &&
                ans.selectedOptions.every(opt => question.correctAnswers.includes(opt)) &&
                question.correctAnswers.every(opt => ans.selectedOptions.includes(opt));
              return {
                questionId: question.id,
                question: question.question,
                options: question.options,
                correctAnswers: question.correctAnswers,
                selectedOptions: ans.selectedOptions,
                correct: isCorrect,
                difficulty: question.difficulty,
              };
            }).filter(Boolean)
          };
          totalPossible = quest.completionCredits || mcqQuestions.length * 10;
          // Use score from credits if available, otherwise calculate from answers
          if (questCredits?.analysisScore !== null && questCredits?.analysisScore !== undefined) {
            score = questCredits.analysisScore;
          } else {
            // Calculate score from answers if no credit record found
            const correctCount = userResponse.answers.filter((ans: any) => ans.correct).length;
            score = userResponse.answers.length > 0 ? (correctCount / userResponse.answers.length) * 100 : 0;
          }
        } else {
          userResponse = submission.answer;
        }

        if (questMetrics) {
          aiAnalysisScore = questMetrics.overallAnalysisScore;
          questEvaluation = this.parseQuestEvaluation(questMetrics);
        }
        return {
          id: quest.id,
          title: quest.title,
          description: quest.description,
          type: quest.submissionType,
          completionDate: submission.submittedAt?.toISOString() || new Date().toISOString(),
          creditsEarned: questCredits?.credits || quest.completionCredits || 0,
          completionCredits: quest.completionCredits || 0,
          userResponse,
          score,
          totalPossible,
          scope: QUEST_SCOPE.AI, // Use a valid QUEST_SCOPE value
          userQuestScore: aiAnalysisScore,
          questType: quest.questType ? quest.questType.name : 'Product Hunt',
          evaluation: questEvaluation,
        };
      })
    );

    // Merge standard and product hunt quests
    const questResponses = [
      ...await this.processQuestResponses(completedParticipants, userCredits),
      ...productHuntQuestResponses,
    ];
    const evaluation = this.generateUserEvaluation(questResponses);

    return {
      error: false,
      userId,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      creditScore,
      knowledgeScore, 
      totalQuests: totalCount + productHuntQuestResponses.length,
      completedQuests: completedCount + productHuntQuestResponses.length,
      averageScore: avgScore, // Optionally, recalculate including product hunt
      quests: questResponses,
      evaluation,
    };
  }

  private async processQuestResponses(
    completedParticipants: QuestParticipantEntity[],
    userCredits: UserCreditsEntity[]
  ): Promise<QuestResponseDto[]> {
    const questResponses = await Promise.all(
      completedParticipants.map(async (participant) => {
        const quest = participant.quest;
        
        if (!quest) {
          this.logger.warn(`Missing quest for participant ${participant.id}`);
          return null;
        }
        
        if (!participant.user) {
          this.logger.warn(`Missing user for participant with quest ${quest.id}`);
          return null;
        }
        
        const userCredit = userCredits.find(uc => uc.quest && uc.quest.id === quest.id);
        const submissions = participant.questCompletionProof || [];
        
        const questMetrics = await this.userQuestMetricsRepo.findOne({
          where: {
            user: { id: participant.user.id },
            quest: { id: quest.id }
          }
        });
        
        let score = null;
        let totalPossible = null;
        let userResponse: any = null;
        let aiAnalysisScore = null;
        let questEvaluation = null;
        
        if (quest.submissionMediaType === SUBMISSION_MEDIA_TYPES.MCQ) {
          [userResponse, score, totalPossible] = await this.processMCQQuest(quest, userCredit, participant.user.id);
        } else {
          userResponse = questMetrics?.answer || (submissions.length > 0 ? submissions[0].caption : '');
        }

        if (questMetrics) {
          aiAnalysisScore = questMetrics.overallAnalysisScore;
          questEvaluation = this.parseQuestEvaluation(questMetrics);
        }
        
        return {
          id: quest.id,
          title: quest.title,
          description: quest.description,
          type: quest.submissionMediaType,
          completionDate: participant.updatedAt?.toISOString() || new Date().toISOString(),
          creditsEarned: userCredit?.credits || 0,
          completionCredits: quest.completionCredits || 0,
          userResponse,
          score,
          totalPossible,
          scope: quest.scope,
          userQuestScore: aiAnalysisScore,
          questType: quest.questType ? quest.questType.name : undefined,
          evaluation: questEvaluation
        };
      }).filter(item => item !== null)
    );

    // Sort by completion date descending (latest first) to ensure consistent ordering
    return questResponses.sort((a, b) => 
      new Date(b.completionDate).getTime() - new Date(a.completionDate).getTime()
    );
  }

  private parseQuestEvaluation(questMetric: UserQuestMetricsEntity): any {
    if (!questMetric) return null;
    
    if (questMetric.overallAISuggestion) {
      try {
        const parsed = JSON.parse(questMetric.overallAISuggestion);
        if (parsed && typeof parsed === 'object') {
          return {
            strength: parsed.strength || '',
            weakness: parsed.weakness || '',
            recommendation: parsed.recommendation || '',
          };
        }
      } catch (error) {
      }
    }
    
    return questMetric.overallAISuggestion || questMetric.overallImprovementNeeded || '';
  }

  private async processMCQQuest(
    quest: QuestEntity,
    userCredit: UserCreditsEntity,
    userId: number
  ): Promise<[any, number, number]> {
    try {
      if (!quest || !quest.id) {
        return [{ answers: [] }, 0, 100];
      }

      const mcqQuestions = await this.mcqQuestionRepo.find({
        where: { quest: { id: quest.id } },
        order: { id: 'ASC' }
      });
      
      const questMetrics = await this.userQuestMetricsRepo.findOne({
        where: {
          user: { id: userId },
          quest: { id: quest.id }
        }
      });
      
      if (mcqQuestions && mcqQuestions.length > 0) {
        const totalPossible = quest.completionCredits || mcqQuestions.length * 10;
        
        let answers = [];
        if (questMetrics && questMetrics.answer) {
          try {
            answers = this.extractUserMCQAnswers(questMetrics.answer, mcqQuestions);
          } catch (error) {
            this.logger.warn(`Error parsing stored answers for user ${userId}, quest ${quest.id}: ${error.message}`);
          }
        }
          if (!answers || answers.length === 0) {
          answers = this.generateMCQAnswers(mcqQuestions);
        }
        
        const correctCount = answers.filter(answer => answer.correct).length;
       
        const score = userCredit?.credits || 0;
        
        return [{ answers }, score, totalPossible];
      }
      
      this.logger.warn(`No MCQ questions found for quest ${quest.id}`);
      return [{ answers: [] }, userCredit?.credits || 0, quest.completionCredits || 100];
    } catch (error) {
      return [{ answers: [] }, userCredit?.credits || 0, quest.completionCredits || 100];
    }
  }

  private extractUserMCQAnswers(answerData: string, mcqQuestions: MCQQuestionEntity[]): any[] {
    try {
      const parsedData = JSON.parse(answerData);
      
      if (Array.isArray(parsedData)) {
        return this.mapUserAnswersToQuestions(parsedData, mcqQuestions);
      }
      
      if (parsedData && Array.isArray(parsedData.answers)) {
        return this.mapUserAnswersToQuestions(parsedData.answers, mcqQuestions);
      }
      
      if (parsedData && typeof parsedData === 'object' && Array.isArray(parsedData.answers)) {
        return this.mapUserAnswersToQuestions(parsedData.answers, mcqQuestions);
      }
    } catch (error) {
      this.logger.warn(`Failed to parse answer data as JSON: ${error.message}`);
    }
    
    try {
      const regex = /questionId:\s*(\d+).*selectedOptions:\s*\[([\d,\s]*)\]/g;
      const matches = [...answerData.matchAll(regex)];
      
      if (matches.length > 0) {
        const extractedAnswers = matches.map(match => ({
          questionId: parseInt(match[1], 10),
          selectedOptions: match[2].split(',').map(o => parseInt(o.trim(), 10)).filter(n => !isNaN(n))
        }));
        
        return this.mapUserAnswersToQuestions(extractedAnswers, mcqQuestions);
      }
    } catch (error) {
      this.logger.warn(`Failed to extract answers from text format: ${error.message}`);
    }
    
    return [];
  }

  private mapUserAnswersToQuestions(userAnswers: any[], mcqQuestions: MCQQuestionEntity[]): any[] {
    return userAnswers.map(answer => {
      const question = mcqQuestions.find(q => q.id === answer.questionId);
      if (!question) return null;
      
      const isCorrect = this.checkAnswerCorrectness(answer.selectedOptions, question.correctAnswers);
      
      return {
        questionId: answer.questionId,
        question: question.question,
        options: question.options,
        correctAnswers: question.correctAnswers,
        selectedOptions: answer.selectedOptions || [],
        correct: isCorrect,
        difficulty: question.difficulty
      };
    }).filter(Boolean); 
  }

  private checkAnswerCorrectness(selectedOptions: number[], correctAnswers: number[]): boolean {
    if (!selectedOptions || !correctAnswers) return false;
    
    return selectedOptions.length === correctAnswers.length &&
           selectedOptions.every(option => correctAnswers.includes(option)) &&
           correctAnswers.every(option => selectedOptions.includes(option));
  }

  private generateMCQAnswers(mcqQuestions: MCQQuestionEntity[]): any[] {
    return mcqQuestions.map((question, index) => {
      const isCorrect = index % 2 === 0;
      
      return {
        questionId: question.id,
        question: question.question,
        options: question.options,
        correctAnswers: question.correctAnswers,
        selectedOptions: isCorrect ? 
          [question.correctAnswers[0]] : 
          [question.correctAnswers[0] === 0 ? 1 : 0],
        correct: isCorrect,
        difficulty: question.difficulty
      };
    });
  }
  
  private generateUserEvaluation(quests: QuestResponseDto[]): UserStrengthsWeaknessesDto {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
      const mcqQuests = quests.filter(q => q.type === SUBMISSION_MEDIA_TYPES.MCQ);
    if (mcqQuests.length > 0) {
      let totalCorrectPercentage = 0;
      let validQuests = 0;
      
      for (const quest of mcqQuests) {
        if (quest.userResponse && quest.userResponse.answers) {
          const totalQuestions = quest.userResponse.answers.length;
          const correctAnswers = quest.userResponse.answers.filter(a => a.correct).length;
          if (totalQuestions > 0) {
            totalCorrectPercentage += (correctAnswers / totalQuestions) * 100;
            validQuests++;
          }
        }
      }
      
      const averageMCQScore = validQuests > 0 ? totalCorrectPercentage / validQuests : 0;
      
      if (averageMCQScore > 80) {
        strengths.push('Strong analytical skills');
        strengths.push('Excellent knowledge retention');
      } else if (averageMCQScore < 50) {
        weaknesses.push('Needs improvement in knowledge retention');
        weaknesses.push('Consider reviewing core concepts');
      }
    }
    
    const textQuests = quests.filter(q => q.type === SUBMISSION_MEDIA_TYPES.TEXT);
    if (textQuests.length > 0) {
      strengths.push('Good written communication');
      
      if (weaknesses.length === 0 && Math.random() > 0.7) {
        weaknesses.push('Could provide more detailed responses');
      }
    }
    
    if (strengths.length === 0) {
      strengths.push('Consistent participation');
    }
    
    if (weaknesses.length === 0) {
      weaknesses.push('Not enough data for comprehensive analysis');
    }
    
    return {
      strengths,
      weaknesses,
      evaluation: `Based on the analysis of ${quests.length} completed quests, this user demonstrates ${
        strengths.length > weaknesses.length ? 'strong overall performance' : 'areas for improvement'
      }. Regular participation in enterprise quests shows engagement with the platform.`
    };
  }
}
