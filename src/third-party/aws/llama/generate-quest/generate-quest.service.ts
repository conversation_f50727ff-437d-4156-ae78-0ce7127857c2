import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { plainToInstance } from 'class-transformer';
import { QuestEntity, QuestTypesEntity } from 'src/models/quest-entity';
import { AIgeneratedQuestDetailsDto } from 'src/quest/AI-quest/AI-quest-dto';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';

@Injectable()
export class AWSLlamaAIService {
  private readonly logger = new Logger(AWSLlamaAIService.name);
  private AIGenerationLimit = 1;
  private MaxRegenLimit = 3;
  private bedrockClient: BedrockRuntimeClient;

  constructor(private configService: ConfigService) {
    // Initialize AWS Bedrock client
    this.bedrockClient = new BedrockRuntimeClient({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });
  }

  // ---------Generates a prompt string based on quest type and user parameters---------
  private getQuestPrompt(
    questType: QuestTypesEntity,
    difficultyLevel: string,
    workLocation: string,
  ): string {
    const promptMapping: Record<string, string> = {
      FITNESS_QUEST: 'FITNESS_QUEST_PROMPT',
      PUZZLE_QUEST: 'PUZZLES_QUEST_PROMPT',
      SHORT_VIDEO_QUEST: 'SHORTS_VIDEO_QUEST_PROMPT',
      PHOTOGRAPHY_QUEST: 'PHOTOGRAPHY_QUEST_PROMPT',
      CODING_QUEST: 'CODING_QUEST_PROMPT',
    };

    const configKey = promptMapping[questType.value];

    if (!configKey) {
      throw new BadRequestException('Invalid Private Quest Type provided...');
    }

    const basePrompt = this.configService.get<string>(configKey);

    if (!basePrompt) {
      throw new BadRequestException(
        `Prompt template for ${configKey} not found in environment variables`,
      );
    }

    // Replace placeholders with actual values
    return basePrompt
      .replace(/{LEVEL}/g, difficultyLevel)
      .replace(/{LOCATION}/g, workLocation);
  }

  async generateContentFromAI(prompt: string): Promise<string> {
    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'), // Updated model ID
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt, // Use the single prompt string
          temperature: 0.8,
          top_p: 0.9,
          max_gen_len: 1000,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      // Parse the response body
      const responseBody = new TextDecoder().decode(response.body);

      const parsedResponse = JSON.parse(responseBody);
      // Extract the generated content from the response
      return parsedResponse.generation || ''; // Adjust based on the actual API response structure
    } catch (error) {
      this.logger.error(`Bedrock API call failed: ${error.message}`);
      throw new InternalServerErrorException('AI Quest generation failed.');
    }
  }

  private parseQuestResponse(
    responseContent: string,
    questType: string,
  ): AIgeneratedQuestDetailsDto[] {
    try {
      let quests: any[];

      // Try to extract JSON from the response
      const jsonMatch = responseContent.match(/\[\s*\{.*?\}\s*\]/s);

      if (jsonMatch) {
        try {
          quests = JSON.parse(jsonMatch[0]);
        } catch (parseError) {
          // If direct parsing fails, try more aggressive cleaning
          const cleanedJson = jsonMatch[0]
            .replace(/'/g, '"')
            .replace(/(\w+):/g, '"$1":')
            .replace(/\n/g, ' ');

          quests = JSON.parse(cleanedJson);
        }
      } else {
        // Fallback to existing JSON matching logic
        const jsonMatches = responseContent.match(/\{[^{}]+\}/g);

        if (!jsonMatches || jsonMatches.length < 2) {
          this.logger.error('Insufficient JSON objects found in the response');
          return [];
        }

        quests = jsonMatches.slice(0, 2).map((jsonStr) => {
          const cleanJsonStr = jsonStr
            .replace(/'/g, '"')
            .replace(/(\w+):/g, '"$1":')
            .replace(/\n/g, ' ');

          return JSON.parse(cleanJsonStr);
        });
      }

      const filteredQuests = quests.map((quest: any) => ({
        'Quest Title': quest['Quest Title'] || quest.title,
        'Quest Description': Array.isArray(quest['Quest Description'])
          ? quest['Quest Description'].join('\n')
          : quest['Quest Description'] || quest.description,
        'Proof of Completion':
          quest['Proof of Completion'] || quest.proofOfCompletion,
      }));

      return filteredQuests.map((quest: any, index: number) =>
        plainToInstance(AIgeneratedQuestDetailsDto, {
          ...quest,
          proofOfCompletion:
            questType === 'PHOTOGRAPHY_QUEST'
              ? 'image'
              : quest['Proof of Completion'] || quest.proofOfCompletion,
          questTypeValue: questType,
          isActive: index === 0,
        }),
      );
    } catch (error) {
      this.logger.error(
        `Failed to parse AI response for quest type ${questType}: ${error.message}`,
      );
      console.error('Parsing error details:', error);
      return [];
    }
  }

  // ---------Main method to generate quests---------
  async generateQuestSuggestion(
    selectedQuestTypes: QuestTypesEntity[],
    difficultyLevel: string,
    workLocation: string,
  ): Promise<AIgeneratedQuestDetailsDto[] | null> {
    if (!selectedQuestTypes || !Array.isArray(selectedQuestTypes)) {
      throw new BadRequestException('Invalid quest types array');
    }

    if (!difficultyLevel || !workLocation) {
      throw new BadRequestException(
        'Difficulty level and work location are required',
      );
    }

    try {
      const questPromises = selectedQuestTypes.map(async (type) => {
        const prompt = this.getQuestPrompt(type, difficultyLevel, workLocation);
        const contentString = await this.generateContentFromAI(prompt);
        return { questType: type.value, contentString };
      });

      const generatedQuests = await Promise.all(questPromises);

      const results = generatedQuests.flatMap(({ questType, contentString }) =>
        this.parseQuestResponse(contentString, questType),
      );

      if (
        results.length < selectedQuestTypes.length * 2 &&
        this.AIGenerationLimit <= this.MaxRegenLimit
      ) {
        this.AIGenerationLimit++;
        return this.generateQuestSuggestion(
          selectedQuestTypes,
          difficultyLevel,
          workLocation,
        );
      }

      this.AIGenerationLimit = 1;
      return results;
    } catch (error) {
      this.logger.error(`Quest generation failed: ${error.message}`);

      return null;

      //   throw new BadRequestException(
      //     'Something wrong with AI Quest generation. Please try again later.',
      //   );
    }
  }

  // ---------Calls the OpenAI API to generate quest content---------
  async ytLinkFromDescription(description: string): Promise<string> {
    // Construct the prompt with the description, ensuring it follows the required format.
    let prompt = `
    YOUR TASK: PROVIDE ONLY ONE YOUTUBE LINK

You are required to find the most relevant YouTube video that closely matches or directly addresses the title provided.
The video must match the topic of the title and be the most fitting choice among available videos.
The video must be available on YouTube, meaning it is not banned or age-restricted.

FORMAT REQUIREMENTS – STRICTLY ONE YOUTUBE LINK ONLY
Your response must be just a single YouTube URL.
No extra text, explanation, or formatting.

PRIORITY: RELEVANT VIDEO
Focus on providing a highly relevant video that directly aligns with the title.
The URL you return should lead to a video that clearly addresses the title posed by the prompt.

Title: $$description

FAILURE TO FOLLOW INSTRUCTIONS WILL RESULT IN INVALID RESPONSE
Any response that does not meet the format of providing only one YouTube URL will be rejected.
Do not offer more than one video link.
No additional suggestions, no justification, no further context. Only the URL, nothing else.
    `;

    if (!description) {
      throw new Error('Description cannot be empty');
    }

    try {
      // Replace the placeholder with the description and remove unnecessary line breaks for cleaner prompt
      const formattedPrompt = prompt
        .replace('$$description', description)
        .replace(/\n/g, ' ');

      // Prepare the request body for the model invocation
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'), // Ensure this model ID is valid
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt: formattedPrompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 1000,
        }),
      };

      // Send the request to the Bedrock client
      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);

      // Decode the response body from the API call
      const responseBody = new TextDecoder().decode(response.body);

      // Use a regex to extract YouTube video links from the response
      const youtubeUrlPattern = /https:\/\/www\.youtube\.com\/watch\?v=[\w-]+/g;
      const match = responseBody.match(youtubeUrlPattern);

      // Return the first valid YouTube URL that is not a placeholder
      return match?.find((url) => !url.includes('VIDEOID')) || '';
    } catch (error) {
      // Log the error and rethrow a more user-friendly exception
      this.logger.error(`Error in ytLinkFromDescription: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to generate YouTube link from description.',
      );
    }
  }

  // ************************ Text Quest Analysis Functions ************************

  private sanitizeAIResponse(responseBody: string): string {
    try {
      // Try to parse the responseBody as JSON
      const parsed = JSON.parse(responseBody);

      // Get the 'generation' field (if present)
      let text = parsed.generation || '';

      // Clean up: remove leading/trailing whitespace and newlines
      text = text.trim();

      // Optionally, replace multiple newlines with a single space
      text = text.replace(/\n+/g, ' ');

      return text;
    } catch (e) {
      // If parsing fails, fallback to a basic cleanup
      return responseBody
        .replace(/\\n/g, ' ') // Replace escaped newlines with spaces
        .replace(/\\"/g, '"') // Replace escaped quotes with actual quotes
        .trim();
    }
  }

  private async getAnalysisScoreFromAI(
    quest: QuestEntity,
    answer: string,
  ): Promise<number> {
    let prompt = '';
    // Use a separate prompt for Product Hunt TEXT quests
    if (quest.questType && quest.questType.value === 'PRODUCT_QUEST') {
      // Use the product hunt prompt with full context
      const productSummary =
        (quest as any).productSummaryContext || quest.description;
      prompt = (process.env.PRODUCT_HUNT_TEXT_ANALYSIS_SCORE_PROMPT || '')
        .replace('$$$summary', productSummary)
        .replace('$$$description', quest.description)
        .replace('$$$answer', answer);
    } else {
      // Use the default text analysis prompt
      prompt = (process.env.TEXT_ANALYSIS_SCORE_PROMPT || '')
        .replace('$$$description', quest.description)
        .replace('$$$answer', answer);
    }

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 100,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);
      const cleaned = this.sanitizeAIResponse(responseBody);

      const score = parseInt(cleaned);
      return Number.isFinite(score) && score >= 0 && score <= 100 ? score : 0;
    } catch (error) {
      this.logger.error(`Error in getAnalysisScoreFromAI: ${error.message}`);
      return 0;
    }
  }

  private async getSuggestionFromAI(
    quest: QuestEntity,
    answer: string,
  ): Promise<string> {
    let prompt = '';

    try {
      // Use different prompts for Product Hunt vs regular TEXT quests
      if (quest.questType && quest.questType.value === 'PRODUCT_QUEST') {
        // Use Product Hunt specific prompt with full context
        const productSummary =
          (quest as any).productSummaryContext || quest.description;
        prompt = (
          process.env.PRODUCT_HUNT_TEXT_ANALYSIS_SUGGESTION_PROMPT ||
          process.env.TEXT_ANALYSIS_SUGGESTION_PROMPT ||
          ''
        )
          .replace('$$$summary', productSummary)
          .replace('$$$description', quest.description)
          .replace('$$$answer', answer);
      } else {
        // Use the default text analysis prompt
        prompt = (process.env.TEXT_ANALYSIS_SUGGESTION_PROMPT || '')
          .replace('$$$description', quest.description)
          .replace('$$$answer', answer);
      }

      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 100,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      return (
        this.sanitizeAIResponse(responseBody) ||
        'Consider adding more details and examples to improve your answer.'
      );
    } catch (error) {
      this.logger.error(`Error in getSuggestionFromAI: ${error.message}`);
      return 'Consider adding more details and examples to improve your answer.';
    }
  }

  private async getImprovementNeededFromAI(
    quest: QuestEntity,
    answer: string,
  ): Promise<string> {
    let prompt = '';

    try {
      // Use different prompts for Product Hunt vs regular TEXT quests
      if (quest.questType && quest.questType.value === 'PRODUCT_QUEST') {
        // Use Product Hunt specific prompt with full context
        const productSummary =
          (quest as any).productSummaryContext || quest.description;
        prompt = (
          process.env.PRODUCT_HUNT_TEXT_ANALYSIS_IMPROVEMENT_PROMPT ||
          process.env.TEXT_ANALYSIS_IMPROVEMENT_PROMPT ||
          ''
        )
          .replace('$$$summary', productSummary)
          .replace('$$$description', quest.description)
          .replace('$$$answer', answer);
      } else {
        // Use the default text analysis prompt
        prompt = (process.env.TEXT_ANALYSIS_IMPROVEMENT_PROMPT || '')
          .replace('$$$description', quest.description)
          .replace('$$$answer', answer);
      }

      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 100,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      return (
        this.sanitizeAIResponse(responseBody) ||
        'Focus on providing more specific details and ensuring clarity in your response.'
      );
    } catch (error) {
      this.logger.error(
        `Error in getImprovementNeededFromAI: ${error.message}`,
      );
      return 'Focus on providing more specific details and ensuring clarity in your response.';
    }
  }
  // ************************ MCQ Quest Analysis Functions ************************
  async getOverallMCQQuestAnalysis(
    quest: QuestEntity,
    correctAnswers: number,
    totalQuestions: number,
    questionsWithAnswers?: any[],
  ): Promise<{
    analysisScore: number;
    evaluation: {
      strength: string;
      weakness: string;
      recommendation: string;
    };
  }> {
    try {
      const correctPercentage = Math.round(
        (correctAnswers / totalQuestions) * 100,
      );

      // Build a detailed analysis of individual questions if provided
      let questionAnalysisText = '';
      if (questionsWithAnswers && questionsWithAnswers.length > 0) {
        questionAnalysisText = questionsWithAnswers
          .map((q, idx) => {
            const shortenedQuestion =
              q.question.length > 100
                ? `${q.question.substring(0, 100).trim()}...`
                : q.question;
            return `Q${idx + 1}: "${shortenedQuestion}" - ${q.isCorrect ? 'Correct' : 'Incorrect'} answer`;
          })
          .join('\n');
      }

      // Enhanced prompt for professional HR-focused analysis
      const prompt = process.env.MCQ_ANALYSIS_IMPROVEMENT_PROMPT.replace(
        '$$$title',
        quest.title,
      )
        .replace('$$$description', quest.description)
        .replace('$$$totalQuestions', totalQuestions.toString())
        .replace('$$$correctAnswers', correctAnswers.toString())
        .replace('$$$correctPercentage', correctPercentage.toString())
        .replace('$$$questionAnalysisText', questionAnalysisText);

      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt: prompt,
          temperature: 0.2, // Reduced temperature for more consistent, professional responses
          top_p: 0.9,
          max_gen_len: 800, // Increased max length for more comprehensive analysis
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      try {
        const sanitizedResponse = this.sanitizeAIResponse(responseBody);
        const jsonResponse = JSON.parse(sanitizedResponse);

        // Professional formatting for the response
        return {
          analysisScore:
            jsonResponse.analysisScore ||
            Math.max(300, correctPercentage * 8 + 200),
          evaluation: {
            strength: this.formatEvaluationText(
              jsonResponse.strength ||
                this.generateDetailedStrength(
                  correctAnswers,
                  totalQuestions,
                  quest,
                  questionsWithAnswers,
                ),
            ),
            weakness: this.formatEvaluationText(
              jsonResponse.weakness ||
                this.generateDetailedWeakness(
                  correctAnswers,
                  totalQuestions,
                  quest,
                  questionsWithAnswers,
                ),
            ),
            recommendation: this.formatEvaluationText(
              jsonResponse.recommendation ||
                this.generateDetailedRecommendation(
                  correctAnswers,
                  totalQuestions,
                  quest,
                  questionsWithAnswers,
                ),
            ),
          },
        };
      } catch (parseError) {
        this.logger.error(`Failed to parse AI response: ${parseError.message}`);

        // Fallback response structure using the available question data
        return {
          analysisScore: Math.max(300, correctPercentage * 8 + 200),
          evaluation: {
            strength: this.formatEvaluationText(
              this.generateDetailedStrength(
                correctAnswers,
                totalQuestions,
                quest,
                questionsWithAnswers,
              ),
            ),
            weakness: this.formatEvaluationText(
              this.generateDetailedWeakness(
                correctAnswers,
                totalQuestions,
                quest,
                questionsWithAnswers,
              ),
            ),
            recommendation: this.formatEvaluationText(
              this.generateDetailedRecommendation(
                correctAnswers,
                totalQuestions,
                quest,
                questionsWithAnswers,
              ),
            ),
          },
        };
      }
    } catch (error) {
      this.logger.error(
        `Error in getOverallMCQQuestAnalysis: ${error.message}`,
      );

      // Fallback response
      const correctPercentage = Math.round(
        (correctAnswers / totalQuestions) * 100,
      );
      return {
        analysisScore: Math.max(300, correctPercentage * 8 + 200),
        evaluation: {
          strength: this.formatEvaluationText(
            this.generateDetailedStrength(
              correctAnswers,
              totalQuestions,
              quest,
              questionsWithAnswers,
            ),
          ),
          weakness: this.formatEvaluationText(
            this.generateDetailedWeakness(
              correctAnswers,
              totalQuestions,
              quest,
              questionsWithAnswers,
            ),
          ),
          recommendation: this.formatEvaluationText(
            this.generateDetailedRecommendation(
              correctAnswers,
              totalQuestions,
              quest,
              questionsWithAnswers,
            ),
          ),
        },
      };
    }
  }

  // Format evaluation text for professional presentation
  private formatEvaluationText(text: string): string {
    // Ensure text doesn't have unnecessary truncation markers
    return text
      .replace(/\.\.\."/g, '"')
      .replace(/"\.\.\./g, '"')
      .replace(/\s{2,}/g, ' ')
      .trim();
  }

  // Enhanced MCQ Detailed Evaluation Helper Methods
  private generateDetailedStrength(
    correctAnswers: number,
    totalQuestions: number,
    quest: QuestEntity,
    questionsWithAnswers: any[],
  ): string {
    const correctPercentage = Math.round(
      (correctAnswers / totalQuestions) * 100,
    );
    const questTitle = quest?.title || 'Assessment';

    // If we have specific questions data, use it for a more detailed evaluation
    if (questionsWithAnswers?.length > 0) {
      const correctQuestions = questionsWithAnswers.filter((q) => q.isCorrect);

      if (correctQuestions.length === 0) {
        return `The assessment results indicate no correct answers on the "${questTitle}" assessment. This suggests a need for comprehensive review of all core concepts covered in this assessment.`;
      }

      // Get topic areas from correct questions
      const topicAreas = correctQuestions
        .map((q) => {
          const words = q.question.split(' ').slice(0, 8);
          return words.join(' ');
        })
        .slice(0, 3);

      const topicsStr =
        topicAreas.length > 0
          ? `Specifically, knowledge proficiency was demonstrated in areas addressing: ${topicAreas.map((t) => `"${t}..."`).join(', ')}`
          : '';

      if (correctPercentage === 100) {
        return `The assessment results demonstrate comprehensive mastery of "${questTitle}". All ${totalQuestions} questions were answered correctly, indicating complete understanding of the subject matter. ${topicsStr}`;
      }

      if (correctPercentage >= 80) {
        return `The assessment results demonstrate strong proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions). ${topicsStr}`;
      }

      if (correctPercentage >= 60) {
        return `The assessment results demonstrate moderate proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions). ${topicsStr}`;
      }

      if (correctPercentage >= 40) {
        return `The assessment results demonstrate basic proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions). ${topicsStr}`;
      }

      return `The assessment results demonstrate limited proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions). ${topicsStr}`;
    }

    // Generic fallbacks if no specific questions data available
    if (correctAnswers === 0) {
      return `The assessment results indicate no correct answers on the "${questTitle}" assessment. This suggests a need for comprehensive review of all core concepts.`;
    }

    if (correctPercentage === 100) {
      return `The assessment results demonstrate comprehensive mastery of "${questTitle}". All ${totalQuestions} questions were answered correctly, indicating complete understanding of the subject matter.`;
    }

    if (correctPercentage >= 80) {
      return `The assessment results demonstrate strong proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions).`;
    }

    if (correctPercentage >= 60) {
      return `The assessment results demonstrate moderate proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions).`;
    }

    if (correctPercentage >= 40) {
      return `The assessment results demonstrate basic proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions).`;
    }

    return `The assessment results demonstrate limited proficiency in "${questTitle}" with ${correctPercentage}% accuracy (${correctAnswers} of ${totalQuestions} questions).`;
  }

  private generateDetailedWeakness(
    correctAnswers: number,
    totalQuestions: number,
    quest: QuestEntity,
    questionsWithAnswers: any[],
  ): string {
    const correctPercentage = Math.round(
      (correctAnswers / totalQuestions) * 100,
    );
    const incorrectAnswers = totalQuestions - correctAnswers;
    const questTitle = quest?.title || 'Assessment';

    // If we have specific questions data, use it for a more detailed evaluation
    if (questionsWithAnswers?.length > 0) {
      const incorrectQuestions = questionsWithAnswers.filter(
        (q) => !q.isCorrect,
      );

      if (incorrectQuestions.length === 0) {
        return `The assessment results indicate no knowledge gaps in the "${questTitle}" assessment. All questions were answered correctly, demonstrating comprehensive understanding across all tested concepts.`;
      }

      // Get topic areas from incorrect questions
      const topicAreas = incorrectQuestions
        .map((q) => {
          const words = q.question.split(' ').slice(0, 8);
          return words.join(' ');
        })
        .slice(0, 3);

      const topicsStr =
        topicAreas.length > 0
          ? `The assessment identified development opportunities in the following areas: ${topicAreas.map((t) => `"${t}..."`).join(', ')}`
          : '';

      if (correctPercentage >= 80) {
        return `The assessment results indicate minor knowledge gaps (${100 - correctPercentage}% of questions) in the "${questTitle}" assessment. ${topicsStr}`;
      }

      if (correctPercentage >= 60) {
        return `The assessment results indicate moderate knowledge gaps (${100 - correctPercentage}% of questions) in the "${questTitle}" assessment. ${topicsStr}`;
      }

      if (correctPercentage >= 40) {
        return `The assessment results indicate significant knowledge gaps (${100 - correctPercentage}% of questions) in the "${questTitle}" assessment. ${topicsStr}`;
      }

      if (correctPercentage > 0) {
        return `The assessment results indicate substantial knowledge gaps (${100 - correctPercentage}% of questions) in the "${questTitle}" assessment. ${topicsStr}`;
      }

      return `The assessment results indicate comprehensive knowledge gaps in the "${questTitle}" assessment, with no correct answers. ${topicsStr}`;
    }

    // Generic fallbacks if no specific questions data available
    if (correctPercentage === 100) {
      return `The assessment results indicate no knowledge gaps in the "${questTitle}" assessment. All questions were answered correctly, demonstrating comprehensive understanding across all tested concepts.`;
    }

    if (correctPercentage >= 80) {
      return `The assessment results indicate minor knowledge gaps (${incorrectAnswers} of ${totalQuestions} questions) in the "${questTitle}" assessment, representing development opportunities in ${100 - correctPercentage}% of the tested concepts.`;
    }

    if (correctPercentage >= 60) {
      return `The assessment results indicate moderate knowledge gaps (${incorrectAnswers} of ${totalQuestions} questions) in the "${questTitle}" assessment, representing development opportunities in ${100 - correctPercentage}% of the tested concepts.`;
    }

    if (correctPercentage >= 40) {
      return `The assessment results indicate significant knowledge gaps (${incorrectAnswers} of ${totalQuestions} questions) in the "${questTitle}" assessment, representing development opportunities in ${100 - correctPercentage}% of the tested concepts.`;
    }

    if (correctPercentage > 0) {
      return `The assessment results indicate substantial knowledge gaps (${incorrectAnswers} of ${totalQuestions} questions) in the "${questTitle}" assessment, representing development opportunities in ${100 - correctPercentage}% of the tested concepts.`;
    }

    return `The assessment results indicate comprehensive knowledge gaps in the "${questTitle}" assessment, with no correct answers. This suggests a need for fundamental training across all related concepts.`;
  }

  private generateDetailedRecommendation(
    correctAnswers: number,
    totalQuestions: number,
    quest: QuestEntity,
    questionsWithAnswers: any[],
  ): string {
    const correctPercentage = Math.round(
      (correctAnswers / totalQuestions) * 100,
    );
    const questTitle = quest?.title || 'Assessment';

    // If we have specific questions data, use it for a more detailed recommendation
    if (questionsWithAnswers?.length > 0) {
      const incorrectQuestions = questionsWithAnswers.filter(
        (q) => !q.isCorrect,
      );

      if (incorrectQuestions.length === 0) {
        return `Based on the exemplary performance demonstrated in the "${questTitle}" assessment, consider advancing to more complex material, mentoring others on these topics, or applying this knowledge in practical implementation scenarios.`;
      }

      // Get topic areas from incorrect questions for targeted recommendations
      const topicAreas = incorrectQuestions
        .map((q) => {
          const words = q.question.split(' ').slice(0, 6);
          return words.join(' ');
        })
        .slice(0, 2);

      const specificTopicsStr =
        topicAreas.length > 0
          ? `with particular focus on concepts related to ${topicAreas.map((t) => `"${t}..."`).join(' and ')}`
          : '';

      if (correctPercentage >= 80) {
        return `Recommended development: Review specific concepts from the "${questTitle}" assessment ${specificTopicsStr}. Consider targeted refresher training or practical application exercises to address these minor knowledge gaps.`;
      }

      if (correctPercentage >= 60) {
        return `Recommended development: Schedule focused review of the "${questTitle}" subject matter ${specificTopicsStr}. Consider supplementary learning resources, guided practice exercises, or mentoring sessions to strengthen understanding of these concepts.`;
      }

      if (correctPercentage >= 40) {
        return `Recommended development: Undertake structured learning on the "${questTitle}" subject matter ${specificTopicsStr}. Consider comprehensive training modules, guided study sessions, and practical application exercises to establish stronger foundations in these areas.`;
      }

      if (correctPercentage > 0) {
        return `Recommended development: Engage in comprehensive training on "${questTitle}" fundamentals ${specificTopicsStr}. Consider beginning with introductory materials and progressing through structured learning paths with regular knowledge checks.`;
      }

      return `Recommended development: Initiate foundational training in all aspects of "${questTitle}". Begin with basic concepts and principles before progressing to more advanced material, with regular assessment to ensure knowledge retention.`;
    }

    // Generic but professional fallbacks if no specific questions data available
    if (correctPercentage === 100) {
      return `Based on the exemplary performance demonstrated in the "${questTitle}" assessment, consider advancing to more complex material, mentoring others on these topics, or applying this knowledge in practical implementation scenarios.`;
    }

    if (correctPercentage >= 80) {
      return `Recommended development: Review specific concepts from the "${questTitle}" assessment. Consider targeted refresher training or practical application exercises to address these minor knowledge gaps and advance existing proficiency.`;
    }

    if (correctPercentage >= 60) {
      return `Recommended development: Schedule focused review of the "${questTitle}" subject matter. Consider supplementary learning resources, guided practice exercises, or mentoring sessions to strengthen understanding of key concepts.`;
    }

    if (correctPercentage >= 40) {
      return `Recommended development: Undertake structured learning on the "${questTitle}" subject matter. Consider comprehensive training modules, guided study sessions, and practical application exercises to establish stronger foundations in these areas.`;
    }

    if (correctPercentage > 0) {
      return `Recommended development: Engage in comprehensive training on "${questTitle}" fundamentals. Consider beginning with introductory materials and progressing through structured learning paths with regular knowledge checks.`;
    }

    return `Recommended development: Initiate foundational training in all aspects of "${questTitle}". Begin with basic concepts and principles before progressing to more advanced material, with regular assessment to ensure knowledge retention.`;
  }

  // ************************ Main Quests Analysis Functions ************************

  async getQuestAnalysis(
    quest: QuestEntity,
    answer: string,
  ): Promise<{
    analysisScore: number;
    aiSuggestion: string;
    improvementNeeded: string;
  }> {
    const [analysisScore, aiSuggestion, improvementNeeded] = await Promise.all([
      this.getAnalysisScoreFromAI(quest, answer),
      this.getSuggestionFromAI(quest, answer),
      this.getImprovementNeededFromAI(quest, answer),
    ]);

    return {
      analysisScore,
      aiSuggestion,
      improvementNeeded,
    };
  }

  async getMCQQuestAnalysis(
    quest: QuestEntity,
    correctAnswers: number,
    totalQuestions: number,
    questionsWithAnswers?: any[],
  ): Promise<{
    analysisScore: number;
    evaluation: {
      strength: string;
      weakness: string;
      recommendation: string;
    };
  }> {
    return await this.getOverallMCQQuestAnalysis(
      quest,
      correctAnswers,
      totalQuestions,
      questionsWithAnswers,
    );
  }

  //   **************************** Product Hunt based quest *********************

  async generateQuestTitleBasedOnContext(
    productSummary: string,
  ): Promise<string> {
    const prompt = `
    You are an expert content creator. Based on the product summary provided below, generate a short, compelling, and relevant title that accurately reflects the core idea of the product.

Guidelines:
1. The title must be concise (maximum 12 words)
2. It should be relevant to the key purpose or value of the product
3. Avoid generic titles like "Product Summary" or "About the Product"
4. Do not include quotation marks or unnecessary punctuation
5. Make it suitable for use as a headline or display title
6. Make sure that title must be precise the productSummary data
7. Create a precise and clear title

Product Summary:
${productSummary}

Return only the title as plain text. Do not include any labels or formatting.

    `;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 30,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      // Parse the generation field
      const parsed = JSON.parse(responseBody);
      const generation = parsed.generation;

      return this.removeExactDuplicatePhrases(generation);
    } catch (error) {
      console.log(error);
      this.logger.error(`Error in getSuggestionFromAI: ${error.message}`);
    }
  }

  removeExactDuplicatePhrases(text: string) {
    if (!text) return '';

    // Normalize whitespace
    const normalized = text.replace(/\s+/g, ' ').trim();

    // Split into words
    const words = normalized.split(' ');

    const length = words.length;

    // Try to find repeated sequences
    for (let size = Math.floor(length / 2); size >= 1; size--) {
      const first = words.slice(0, size).join(' ');
      const second = words.slice(size, size * 2).join(' ');

      if (first === second) {
        return first;
      }
    }

    return normalized;
  }

  async generateQuestBasedOnProductSummaryForText(
    productSummary: string,
    difficulty: string,
  ): Promise<
    { question: string; answer: string; difficulty: string } | null | undefined
  > {
    const prompt = `
You are an expert in creating high-quality open-ended questions that assess understanding of key concepts. Your task is to generate one unique open-ended question and its correct answer based on the content provided below. Content to generate the question from: ${productSummary} Difficulty Level: ${difficulty} Instructions: 1. Identify 5 distinct subtopics or key points from the content. 2. Randomly select one of these subtopics to focus your question on. 3. Create one open-ended question based on the selected subtopic. 4. Provide a clear and specific answer based on the content. 5. Ensure the question tests real understanding, not just surface-level recall. 6. Do not refer to "the text" or "the content" in your question. 7. Adjust the complexity of the question based on the specified difficulty: Easy: Basic facts or definitions; Intermediate: Cause/effect or comparisons; Hard: Conceptual understanding or interpretation; Very Hard: Deep analysis, synthesis, or reasoning. 8. Each time this prompt is run, select a different subtopic to ensure the question and answer are unique. Return ONLY a valid JSON object in the following format: { "question": "[A unique, clear, open-ended question about the subject]", "answer": "[The correct, specific answer based on the content]", "difficulty": "{DIFFICULTY_LEVEL}" }. DO NOT include explanations, lists, or any extra commentary. Return ONLY the JSON.
    `;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 100,
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      // Parse the generation field
      const parsed = JSON.parse(responseBody);
      const generation = parsed.generation;

      return this.extractFirstValidQA(generation);
    } catch (error) {
      console.log(error);
      this.logger.error(`Error in getSuggestionFromAI: ${error.message}`);
    }
  }

  extractFirstValidQA(generationString: string) {
    const match = generationString.match(/{[\s\S]*?}/); // Match the first full JSON block
    if (!match) throw new Error('No valid JSON object found in generation.');
    return JSON.parse(match[0]); // Safely parse only the matched JSON
  }

  async generateQuestBasedOnProductSummaryForMCQ(
    productSummary: string,
    numberOfQuestions: number,
    DIFFICULTY_LEVEL: string,
  ): Promise<
    | { question: string; options: string[]; correctAnswer: number }[]
    | null
    | undefined
  > {
    const prompt = `
You are an expert at creating multiple-choice questions. Generate multiple-choice questions based on the following content. Each question should test understanding of key concepts and be at ${DIFFICULTY_LEVEL} difficulty level.\n\nContent to generate questions from:\n$$${productSummary}\n\nDifficulty Level: ${DIFFICULTY_LEVEL}\n\nInstructions:\n1. Create exactly ${numberOfQuestions} multiple-choice questions\n2. Each question must have exactly 5 options (numbered from 0-4)\n3. Each question must have exactly one correct answer\n4. All questions should be at ${DIFFICULTY_LEVEL} difficulty level\n5. Make sure all options are MEANINGFUL and DIRECTLY RELATED to the content\n6. Options should be specific answers, not generic placeholders like "Option A"\n7. NEVER refer to "the text" or "the content" in your questions - write questions that directly address the subject matter\n8. Frame questions as if you're testing knowledge about the subject itself, not about what was mentioned in some text\n9. Adjust question complexity based on the specified difficulty level:\n   - Easy: Basic concepts and straightforward questions\n   - Intermediate: Moderate complexity requiring some analysis\n   - Hard: Complex concepts requiring deep understanding\n   - Very Hard: Advanced concepts requiring critical thinking\n10. Return your response EXACTLY as a valid JSON object with the following structure:\n\n{\n  "questions": [\n    {\n      "question": "[A specific question about the subject matter]",\n      "options": ["A specific option related to content", "Another specific option", "Third specific option", "Fourth specific option", "Fifth specific option"],\n      "correctAnswers": [2],\n      "difficulty": "${DIFFICULTY_LEVEL}"\n    }\n  ]\n}\n\nYour response must be ONLY the clean JSON object above, with no additional text or explanation.
`;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0.2,
          top_p: 0.9,
          max_gen_len: numberOfQuestions * 70, // Increased significantly for multiple questions
        }),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      return this.extractMCQQuestions(responseBody);
    } catch (error) {
      console.log(error);
      this.logger.error(
        `Error in generateQuestBasedOnProductSummaryForMCQ: ${error.message}`,
      );
      return undefined;
    }
  }

  extractMCQQuestions(response: string) {
    const raw = (typeof response === 'string' ? JSON.parse(response) : response)
      .generation;

    // Match the first valid JSON object that includes the "questions" array
    const match = raw.match(/{\s*"questions":\s*\[.*?\]\s*}/s);

    if (!match) {
      throw new Error('No valid questions JSON found in the LLaMA response.');
    }

    try {
      const parsed = JSON.parse(match[0]);
      return parsed.questions;
    } catch (err) {
      throw new Error('Failed to parse JSON: ' + err.message);
    }
  }

  public async generateFourWordTitleForProductHuntQuestionAnswer(
    question: string,
    productSummary: string,
  ): Promise<string> {
    const prompt = `
    Generate a clean and meaningful title using exactly five words. Follow these rules strictly:

1. Use only English alphabetic characters (A–Z, a–z) in each word.
2. Do not use any special characters such as *, #, -, _, ", ~, or punctuation of any kind.
3. Do not include markdown syntax like ###, **, or --- anywhere in the response.
4. Each word should start with a capital letter (Title Case).
5. The title must reflect the main concept or topic the question is asking about.
6. Do not reveal or imply the answer in the title.
7. Do not copy full phrases from the question or summary.
8. Do not add framing phrases like “Title:”, “Here is your title:”, “Answer:”, etc.

Inputs:

Product Summary: ${productSummary}

Question: ${question}

Respond with only the five-word title as a plain text string. Do not include any special characters, formatting, or labels.

`;
    try {
      const result = await this.generateContentFromAI(prompt);
      console.log(result);
      // Truncate to 5 words, remove quotes and punctuation
      return result
        .replace(/["'.,!?]/g, '')
        .split(/\s+/)
        .slice(0, 5)
        .join(' ');
    } catch (e) {
      this.logger.error('Failed to generate 5-word title:', e);
      return 'Product Quest Title';
    }
  }

  public async generateFourWordTitleAndDescriptionForProductHuntMCQ(
    mcqQuestions: any,
    productSummary: string,
  ): Promise<any> {
    const convertIntoStringMCQQuestions = mcqQuestions.join(', ');

    const prompt = `
You are an AI assistant that creates a 4-word, meaningful title and a short description based on MCQ questions and a product summary.

## Output Format:
Respond only with valid JSON:
{
  "title": "Four Word Title",
  "description": "Short, complete description in 20–30 words."
}

## Title Rules:
1. Use only English alphabetic characters (A–Z, a–z) in each word.
2. Do not use any special characters such as *, #, -, _, ", ~, or punctuation of any kind.
3. Do not include markdown syntax like ###, **, or --- anywhere in the response.
4. Each word should start with a capital letter (Title Case).
5. The title must reflect the main concept or topic the questions are asking about.
6. Do not reveal or imply the answer in the title.
7. Do not copy full phrases from the questions or the summary.
8. Do not add framing phrases like “Title:”, “Here is your title:”, etc.

## Description Rules:
1. Must be a complete sentence or paragraph, not ending mid-thought.
2. Should be concise, clear, and relevant (20–30 words).
3. Should summarize the theme of the questions and how they relate to the product.
4. Must end with proper punctuation (usually a period).
5. Do not include special characters such as *, #, ~, _, ", or markdown symbols.
6. Do not include placeholder or broken sentences.

## Input:

MCQ Questions:
${convertIntoStringMCQQuestions}

Product Summary:
${productSummary}

Return only the JSON object. Do not add any extra text before or after it.

    `;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0,
          top_p: 0.9,
          max_gen_len: 80,
        }),
      };
      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);
      const parsedResponseBody = JSON.parse(responseBody)?.generation;

      return this.extractFirstValidJsonObjectForTitleAndDescription(
        parsedResponseBody,
      );
    } catch (e) {
      this.logger.error('Failed to generate 5-word title:', e);
      return 'Product Quest Title';
    }
  }

  async extractFirstValidJsonObjectForTitleAndDescription(responseText) {
    const regex =
      /\{\s*"title"\s*:\s*"([^"]+)",\s*"description"\s*:\s*"([^"]+)"\s*\}/s;
    const match = responseText.match(regex);

    if (!match) return null;

    try {
      return {
        title: match[1],
        description: match[2],
      };
    } catch {
      return null;
    }
  }

  //   Product hunt MCQ generation
  async generateUniqueMCQForProductHunt(
    productSummary: string,
    previousMCQ: any,
  ) {
    const numberOfQuestions = 5;

    const convertPreviousMCQInString = previousMCQ
      .map((data) => data.question)
      .join(' | ');

    const prompt = `
You are a professional AI assistant trained to generate high-quality multiple-choice questions (MCQs).

Your task:

- Read the product summary provided below.
- Avoid repeating or rephrasing any of the existing MCQs.
- Generate exactly ${numberOfQuestions} new, unique, and insightful MCQs based on the summary.
- The difficulty level should be: Easy

=== PRODUCT SUMMARY ===
${productSummary}

=== EXISTING MCQs (Do not repeat or paraphrase any of these) ===
${convertPreviousMCQInString}

=== OUTPUT REQUIREMENTS ===

You must return a valid **JSON object** with the following structure:

{
  "mcqTitle": "Short, descriptive quiz title related to the summary",
  "questions": [
    {
      "question": "Clearly written, unique question?",
      "options": [
        "Option A",
        "Option B",
        "Option C",
        "Option D",
        "Option E"
      ],
      "correctAnswer": "Option B",
      "explanation": "One sentence explanation why this option is correct."
    },
    {
      "question": "Second unique question?",
      "options": [
        "Option A",
        "Option B",
        "Option C",
        "Option D",
        "Option E"
      ],
      "correctAnswer": "Option E",
      "explanation": "Brief, fact-based explanation."
    }
  ]
}

IMPORTANT INSTRUCTIONS:

- Do NOT include any text before or after the JSON object.
- Do NOT use markdown, quotes, code formatting, or comments.
- The output must be directly parsable as raw JSON (using JSON.parse).
- The JSON must contain only one top-level object.

Your response must be **strictly valid JSON** and nothing else.

    `;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0.3,
          top_p: 0.85,
          max_gen_len: numberOfQuestions * 200,
        }),
      };
      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);
      const finalResponse =
        await this.sanitizeLlamaUniqueMCQForProductHuntResponse(
          JSON.parse(responseBody).generation,
        );

      console.log(finalResponse);
      return finalResponse;
    } catch (error) {
      console.log(error);
      this.logger.error(
        `Error in generateMCQForProductHuntTest function: ${error.message}`,
      );
      return null;
    }
  }

  async sanitizeLlamaUniqueMCQForProductHuntResponse(jsonString) {
    const match = jsonString.match(
      /\{\s*"mcqTitle"\s*:\s*".+?",\s*"questions"\s*:\s*\[[\s\S]*?\]\s*\}/,
    );
    if (!match) throw new Error('No JSON object found in the input text.');
    return JSON.parse(match[0]);
  }

  //   Product hunt text based
  async generateUniqueQuestionForProductHunt(
    productSummary: string,
    previousQuestions: any,
  ) {
    const difficulty = 'easy';

    const convertPreviousQuestionsInString = previousQuestions
      .map((data) => data.question)
      .join(' | ');

    const prompt = `
You are an expert AI assistant designed to generate a single high-quality educational question.

Instructions:

1. Read the following product summary:
${productSummary}

2. Refer to this list of previously asked questions and do not repeat or paraphrase them:
${convertPreviousQuestionsInString}

3. Generate exactly **one** new, unique, and insightful question based on the product summary.

Guidelines:
- The question must test understanding or critical thinking based on the summary.
- Do NOT include multiple-choice options or any explanation.
- Do NOT repeat or reword any previous questions.
- Your response must include a short descriptive title, the question, and a correct answer.
- All fields must be clear, specific, and relevant.

Output Format:
Respond ONLY with a valid JSON object in the following format:

{
  "title": "Short descriptive title based on the question theme",
  "question": "One unique question not present in previousQuestions",
  "answer": "Accurate and correct answer based on the summary"
}

Important:
- Do NOT include any text before or after the JSON.
- Return ONLY valid JSON.

    `;

    try {
      const input = {
        modelId: this.configService.get<string>('OLAMMA_MODEL_ID'),
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt,
          temperature: 0.3,
          top_p: 0.85,
          max_gen_len: 80,
        }),
      };
      const command = new InvokeModelCommand(input);
      const response = await this.bedrockClient.send(command);
      const responseBody = new TextDecoder().decode(response.body);

      const finalResponse = {
        ...(await this.sanitizeLlamaUniqueQuestionsForProductHuntResponse(
          JSON.parse(responseBody).generation,
        )),
        difficulty,
      };

      console.log(
        await this.sanitizeLlamaUniqueQuestionsForProductHuntResponse(
          JSON.parse(responseBody).generation,
        ),
      );

      return finalResponse;
    } catch (error) {
      //   this.logger.error(
      //     `Error in generateUniqueQuestionForProductHunt function: ${error.message}`,
      //   );
      console.log(error);
      return null;
    }
  }

  async sanitizeLlamaUniqueQuestionsForProductHuntResponse(rawString) {
    const match = rawString.match(/\{\s*"title"\s*:\s*".*?"\s*,[\s\S]*?\}/);
    if (!match) throw new Error('No JSON object found in the input text.');
    return JSON.parse(match[0]);
  }
}
