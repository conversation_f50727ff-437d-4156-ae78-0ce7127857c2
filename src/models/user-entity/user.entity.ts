import { IsEmail, IsNotEmpty } from 'class-validator';
import {
  <PERSON>um<PERSON>,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  BeforeI<PERSON>rt,
  ManyToMany,
  JoinTable,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { RoleEntity } from './role.entity';
import { AccessTokenEntity } from './accessToken.entity';
import { EnterpriseEntity } from './enterprise.entity';
import {
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
} from '../quest-entity';
import { CommentEntity, FeedEntity, InteractionEntity } from '../feed-entity';
import { FriendRequestEntity } from '../friends-entity/friend.entity';
import { DepartmentEntity } from './department.entity';
import { ReportEntity } from '../report-entity';
import { UserCreditsEntity } from '../credits-entity';
import { QuestCompletionTrackingEntity } from '../quest-entity/quest-completion-tracking.entity';
import { FeedbackEntity } from '../feedback-entity';
import { NotificationsEntity } from '../notification-entity';
import { AiQuestsGenerationLogEntity } from '../AI-quest-generation-log-entity';
import { ProductSummaryEntity } from '../pdf-parsing-entity';

export const DEFAULT_AVATAR_URL =
  'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/c208e193-31f3-4ddc-9f95-65fe209b72f5-da7ed7b0-5f66-4f97-a610-51100d3b9fd2%20%281%29.jpg';

export enum USER_WORK_LOCATION {
  HOME = 'home',
  OFFICE = 'office',
}

export enum QUEST_DIFFICULTY_TYPES {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
  VERY_HARD = 'very hard',
}

export enum USER_DISABILTY_TYPES {
  YES = 'yes',
  NO = 'no',
  PREFER_NOT_TO_SAY = 'prefer not to say',
}

@Entity('users')
@Index('IDX_EMAIL_ISDELETED_USER', ['email', 'isDeleted'])
export class UserEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @IsNotEmpty()
  @Column({ nullable: true }) // because on register there is no name provided by user until verify
  firstName: string;

  @IsNotEmpty()
  @Column({ nullable: true }) // because on register there is no name provided by user until verify
  lastName: string;

  @Column({ default: DEFAULT_AVATAR_URL })
  avatar: string;

  @IsEmail()
  @Column({ nullable: false })
  email: string;

  @Column({ default: null })
  password: string;

  @Column({ default: null })
  googleId: string;

  @Column({ default: false })
  isAccountVerified: boolean;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: null, nullable: true })
  designation: string;

  @Column({
    type: 'enum',
    enum: USER_DISABILTY_TYPES,
    default: USER_DISABILTY_TYPES.NO,
    nullable: false,
  })
  disability: USER_DISABILTY_TYPES;

  @CreateDateColumn()
  joinedAt: Date;

  @Column({ type: 'boolean', default: false, nullable: false })
  isDeleted: boolean;

  @Column({
    type: 'enum',
    enum: USER_WORK_LOCATION,
    nullable: false,
    default: USER_WORK_LOCATION.OFFICE,
  })
  workLocation: USER_WORK_LOCATION;

  @ManyToOne(() => DepartmentEntity)
  @JoinColumn({ name: 'departmentId' })
  department: DepartmentEntity;

  @ManyToMany(() => RoleEntity, { eager: true })
  @JoinTable()
  roles: RoleEntity[];

  @OneToMany(() => AccessTokenEntity, (accessToken) => accessToken.user)
  accessToken: AccessTokenEntity[];

  @OneToMany(() => FeedbackEntity, (feedback) => feedback.user)
  feedbacks: FeedbackEntity[];

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[];

  @OneToMany(
    () => NotificationsEntity,
    (notification) => notification.enterprise,
  )
  notifications: NotificationsEntity[];

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.users, {
    nullable: true,
  })
  @JoinColumn({ name: 'enterpriseId' })
  enterprise: EnterpriseEntity;

  // --------------------------------- basic quest related columns ---------------------------------
  @ManyToMany(() => QuestTypesEntity, { eager: true })
  @JoinTable()
  selectedQuestTypes: QuestTypesEntity[];

  @OneToMany(() => QuestEntity, (quest) => quest.creator)
  createdQuests: QuestEntity[];

  @OneToMany(() => ReportEntity, (report) => report.user)
  reports: ReportEntity[];

  // ------------------------- enterprise quest related columns ---------------------------------
  @OneToMany(() => QuestParticipantEntity, (paricipants) => paricipants.user)
  questsParticipated: QuestParticipantEntity[];

  // --------------------------------- AI quest related columns ---------------------------------

  @Column({
    type: 'enum',
    enum: QUEST_DIFFICULTY_TYPES,
    nullable: false,
    default: QUEST_DIFFICULTY_TYPES.EASY,
  })
  difficulty: QUEST_DIFFICULTY_TYPES;

  @OneToMany(() => QuestEntity, (quest) => quest.assignedToUser)
  AiQuestsAssigned: QuestEntity[];

  @OneToMany(
    () => QuestCompletionProofMediaEntity,
    (CompletionMedia) => CompletionMedia.userToSubmit,
  )
  AIQuestCompletionProofMedia: QuestCompletionProofMediaEntity[];

  @OneToMany(() => UserCreditsEntity, (credits) => credits.user)
  credits: UserCreditsEntity[];

  @OneToMany(() => QuestCompletionTrackingEntity, (tracking) => tracking.user)
  questCompletionTracking: QuestCompletionTrackingEntity[];

  @OneToMany(() => AiQuestsGenerationLogEntity, (log) => log.user)
  aiGenerationLogs: AiQuestsGenerationLogEntity[];

  // --------------------------------- friends related columns ---------------------------------
  @OneToMany(() => FriendRequestEntity, (request) => request.sender)
  sentFriendRequests: FriendRequestEntity[];

  @OneToMany(() => FriendRequestEntity, (request) => request.receiver)
  receivedFriendRequests: FriendRequestEntity[];

  @OneToMany(() => FeedEntity, (feed) => feed.author)
  createdFeeds: FeedEntity[];

  @OneToMany(() => CommentEntity, (comment) => comment.author)
  commentsGenerated: CommentEntity[];

  @OneToMany(() => InteractionEntity, (interaction) => interaction.user)
  interactions: InteractionEntity[];

  @OneToMany(() => ProductSummaryEntity, (summary) => summary.uploadedBy)
  productSummaries: ProductSummaryEntity[];
}
