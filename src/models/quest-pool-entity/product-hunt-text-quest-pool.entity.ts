import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum PRODUCT_HUNT_QUEST_TYPE {
  MCQ = 'MCQ',
  TEXT = 'TEXT',
}

export enum PRODUCT_HUNT_QUEST_DIFFICULTY {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
  VERY_HARD = 'very hard',
}

@Entity('product_hunt_text_quest_pool')
export class ProductHuntTextQuestPoolEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  question: string;

  @Column({ type: 'text', nullable: true })
  answer: string;

  @Column({
    type: 'enum',
    enum: PRODUCT_HUNT_QUEST_DIFFICULTY,
    default: PRODUCT_HUNT_QUEST_DIFFICULTY.EASY,
  })
  difficulty: PRODUCT_HUNT_QUEST_DIFFICULTY;

  @Column({ type: 'int', nullable: false })
  productSummaryId: number;

  @Column({ type: 'boolean', default: false })
  isAssigned: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
