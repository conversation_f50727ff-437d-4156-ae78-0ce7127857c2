import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ProductSummaryEntity } from '../pdf-parsing-entity/parser.entity';

export enum PRODUCT_HUNT_QUEST_TYPE {
  MCQ = 'MCQ',
  TEXT = 'TEXT',
}

export enum PRODUCT_HUNT_QUEST_DIFFICULTY {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
  VERY_HARD = 'very hard',
}

@Entity('product_hunt_mcq_quest_pool')
export class ProductHuntMCQQuestPoolEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  question: string;

  @Column({ type: 'json', nullable: true })
  mcqOptions: string[];

  @Column({ type: 'text' })
  correctAnswer: string;

  @Column({
    type: 'enum',
    enum: PRODUCT_HUNT_QUEST_DIFFICULTY,
    default: PRODUCT_HUNT_QUEST_DIFFICULTY.EASY,
  })
  difficulty: PRODUCT_HUNT_QUEST_DIFFICULTY;

  @Column({ type: 'int', nullable: false })
  productSummaryId: number;

  @Column({ type: 'boolean', default: false })
  isAssigned: boolean;

  @ManyToOne(() => ProductSummaryEntity, { nullable: true })
  @JoinColumn({ name: 'productSummaryId' })
  productSummary?: ProductSummaryEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
