import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { QuestEntity, QuestTypesEntity } from '../quest-entity';
import { EnterpriseEntity, UserEntity } from '../user-entity';
import { ProductQuestEntity } from '../products-summary-entity/product-quest.entity';

@Entity('user_credits')
export class UserCreditsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'integer' })
  credits: number;

  @Column({ type: 'integer', nullable: true })
  analysisScore: number;

  @Column({ type: 'text', nullable: true })
  answer: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @CreateDateColumn()
  date: Date;

  @Column({ nullable: false })
  submissionDate: string;

  @Column({ nullable: false })
  submissionFullDate: string;

  @ManyToOne(() => QuestEntity, (quest) => quest.id, { nullable: true })
  @JoinColumn({ name: 'quest_id' })
  quest: QuestEntity;

  @ManyToOne(() => ProductQuestEntity, (productQuest) => productQuest.id, {
    nullable: true,
  })
  @JoinColumn({ name: 'product_quest_id' })
  productQuest: ProductQuestEntity;

  @ManyToOne(() => UserEntity, (user) => user.id, { nullable: false })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.id, {
    nullable: false,
  })
  @JoinColumn({ name: 'enterprise_id' })
  enterprise: EnterpriseEntity;

  @ManyToOne(() => QuestTypesEntity, { nullable: true })
  @JoinColumn({ name: 'questType' })
  questType: QuestTypesEntity;
}
