import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { QuestEntity, QuestTypesEntity } from '../quest-entity';
import { EnterpriseEntity, UserEntity } from '../user-entity';
import { UserMCQQuestionMetricsEntity } from './user-mcq-questions-metrics.entity';
import { ProductQuestEntity } from '../products-summary-entity/product-quest.entity';

@Entity('user_quests_metrics')
export class UserQuestMetricsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => QuestEntity, (quest) => quest.id, { nullable: true })
  @JoinColumn({ name: 'quest_id' })
  quest: QuestEntity;

  @ManyToOne(() => ProductQuestEntity, { nullable: true })
  @JoinColumn({ name: 'product_quest_id' })
  productQuest: ProductQuestEntity;

  @ManyToOne(() => QuestTypesEntity, { nullable: false })
  @JoinColumn({ name: 'questType' })
  questType: QuestTypesEntity;

  @ManyToOne(() => UserEntity, (user) => user.id, { nullable: false })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => EnterpriseEntity, (enterprise) => enterprise.id, {
    nullable: false,
  })
  @JoinColumn({ name: 'enterprise_id' })
  enterprise: EnterpriseEntity;

  @Column({ type: 'text', nullable: true }) // will be null in case of mcq questions
  overallAISuggestion: string;

  @Column({ type: 'text', nullable: true }) // will be null in case of mcq questions
  overallImprovementNeeded: string;

  @Column({ type: 'integer', nullable: true })
  overallAnalysisScore: number;

  @Column({ type: 'text', nullable: true })
  answer: string;

  @Column({ type: 'integer', nullable: false })
  credits: number;

  @OneToMany(() => UserMCQQuestionMetricsEntity, (mcq) => mcq.userQuestMetrics)
  userMCQQuestionMetrics?: UserMCQQuestionMetricsEntity[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
