import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { UserEntity } from 'src/models/user-entity';
import { ProductSummaryEntity } from './parser.entity';

export enum PRODUCT_HUNT_LEVEL {
  EASY = 'easy',
  INTERMEDIATE = 'intermediate',
  HARD = 'hard',
}

@Entity('user_product_hunt_level')
export class UserProductHuntLevelEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => UserEntity, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => ProductSummaryEntity, { nullable: false })
  @JoinColumn({ name: 'productSummaryId' })
  productSummary: ProductSummaryEntity;

  @Column({
    type: 'enum',
    enum: PRODUCT_HUNT_LEVEL,
    default: PRODUCT_HUNT_LEVEL.EASY,
  })
  @Index()
  currentLevel: PRODUCT_HUNT_LEVEL;

  @Column({ type: 'date', nullable: false })
  lastLevelChangeDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
