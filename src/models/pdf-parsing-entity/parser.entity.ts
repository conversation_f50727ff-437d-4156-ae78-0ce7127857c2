import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserEntity } from 'src/models/user-entity';

@Entity('product_summary')
export class ProductSummaryEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  @Index()
  title: string;
  @Column('longtext')
  content: string;

  @Column('text')
  summary: string;

  @Column('longtext', { nullable: true })
  structuredContentJson: string;
  @Column('longtext', { nullable: true })
  imageAnalysesJson: string;

  @Column('longtext', { nullable: true })
  extractedImageUrls: string;

  @Column({ type: 'int', nullable: false })
  @Index()
  enterpriseId: number;

  @Column({ type: 'int', nullable: false })
  uploadedByUserId: number;

  @Column({ default: true })
  isEnableToView: boolean;

  @Column({ default: false })
  isDeleted: boolean;

  // PDF source tracking for edit/re-processing functionality
  @Column({ nullable: true, length: 50 })
  sourceType: string; // 'upload', 's3_url', 's3_bucket'

  @Column({ nullable: true, length: 1000 })
  sourceLocation: string; // S3 URL, bucket/key JSON, or original filename

  @Column({ nullable: true, length: 255 })
  originalFileName: string; // Original PDF filename

  @Column({ nullable: true })
  fileSizeBytes: number; // File size for validation

  @Column({ nullable: true })
  totalPages: number; // PDF page count

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'uploadedByUserId' })
  uploadedBy: UserEntity;
}
