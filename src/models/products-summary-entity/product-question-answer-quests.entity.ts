import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProductQuestEntity } from './product-quest.entity';

@Entity('product_question_answers_quests')
export class ProductQuestionAnswerEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: true })
  question: string;

  @Column({ type: 'text', nullable: true })
  answer: string;

  @Column({ type: 'int', nullable: false })
  productQuestId: number;

  @ManyToOne(() => ProductQuestEntity, { eager: false })
  @JoinColumn({ name: 'productQuestId' })
  productQuest: ProductQuestEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
