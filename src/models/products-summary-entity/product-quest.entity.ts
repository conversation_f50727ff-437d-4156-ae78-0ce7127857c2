import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  <PERSON>T<PERSON><PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ProductSummaryEntity } from '../pdf-parsing-entity';
import { UserEntity } from 'src/models/user-entity';
import { QuestTypesEntity } from '../quest-entity/questTypes.entity';

export enum PRODUCT_QUEST_SUBMISSION_TYPE {
  MCQ = 'MCQ',
  TEXT = 'TEXT',
}

@Entity('product_quests')
@Index('IDX_PRODUCT_SUMMARY_QUEST', ['productSummaryId'])
@Index('IDX_ENTERPRISE_PRODUCT_QUEST', ['enterpriseId'])
@Index('IDX_SUBMISSION_TYPE', ['submissionType'])
@Index('IDX_CREATED_BY_USER', ['assignToUserId'])
export class ProductQuestEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  title: string;

  @Column({
    type: 'enum',
    enum: PRODUCT_QUEST_SUBMISSION_TYPE,
    nullable: false,
  })
  submissionType: PRODUCT_QUEST_SUBMISSION_TYPE;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: ['easy', 'intermediate', 'hard', 'very hard'],
    default: 'intermediate',
    nullable: false,
  })
  difficulty: string;

  @Column({ nullable: false })
  completionCredits: number;

  @CreateDateColumn({ nullable: false }) // quest will be started as soon as created.
  startDate: Date;

  @Column({ nullable: false })
  endDate: Date;

  @Column({
    type: 'boolean',
    default: true,
    nullable: false,
  })
  isActive: boolean;

  @Column({ type: 'int', nullable: false })
  productSummaryId: number;

  @Column({ type: 'int', nullable: false })
  enterpriseId: number;

  @Column({ type: 'int', nullable: false })
  assignToUserId: number;

  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[];

  @Column({ type: 'int' })
  questTypeId: number;

  @ManyToOne(() => QuestTypesEntity, { eager: false, nullable: false })
  @JoinColumn({ name: 'questTypeId' })
  questType: QuestTypesEntity;

  // Relations
  @ManyToOne(() => ProductSummaryEntity, { eager: false })
  @JoinColumn({ name: 'productSummaryId' })
  productSummary: ProductSummaryEntity;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'assignToUserId' })
  user: UserEntity;
}
