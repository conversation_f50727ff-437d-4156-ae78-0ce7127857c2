import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>umn,
  <PERSON>To<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from 'typeorm';
import { ProductQuestEntity } from './product-quest.entity';
import { UserEntity } from '../user-entity';

export enum PRODUCT_QUEST_SUBMISSION_TYPE {
  MCQ = 'MCQ',
  TEXT = 'TEXT',
}

@Entity('product_quest_submissions')
export class ProductQuestSubmissionEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductQuestEntity, { nullable: false })
  @JoinColumn({ name: 'productQuestId' })
  productQuest: ProductQuestEntity;

  @ManyToOne(() => UserEntity, { nullable: false })
  @JoinColumn({ name: 'submittedByUserId' })
  submittedByUserId: UserEntity;

  @Column({ type: 'enum', enum: PRODUCT_QUEST_SUBMISSION_TYPE })
  submissionType: PRODUCT_QUEST_SUBMISSION_TYPE;

  // For Q&A (TEXT)
  @Column({ type: 'text', nullable: true })
  answer: string;

  // For MCQ: array of { questionId, selectedOptions }
  @Column({ type: 'json', nullable: true })
  mcqAnswers: { questionId: number; selectedOptions: number[] }[];

  @CreateDateColumn()
  submittedAt: Date;
}
