import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ProductSummaryEntity } from '../pdf-parsing-entity';
import { UserEntity } from 'src/models/user-entity';
import { ProductQuestEntity } from './product-quest.entity';

@Entity('product_mcq_quests')
@Index('IDX_PRODUCT_SUMMARY_MCQ_QUEST', ['productSummaryId'])
@Index('IDX_ENTERPRISE_PRODUCT_MCQ_QUEST', ['enterpriseId'])
export class ProductMCQQuestEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  question: string;

  @Column({ type: 'json' })
  options: string[];

  @Column({ type: 'json' })
  correctAnswers: number[];

  @Column({
    type: 'enum',
    enum: ['easy', 'intermediate', 'hard', 'very hard'],
    default: 'intermediate',
    nullable: false,
  })
  difficulty: string;

  @Column({ type: 'int', nullable: false })
  productSummaryId: number;

  @Column({ type: 'int', nullable: false })
  enterpriseId: number;

  @Column({ type: 'int', nullable: true })
  questId: number;

  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => ProductSummaryEntity, { eager: false })
  @JoinColumn({ name: 'productSummaryId' })
  productSummary: ProductSummaryEntity;

  @ManyToOne(() => UserEntity, { eager: false })
  @JoinColumn({ name: 'assignToUserId' })
  assignToUserId: UserEntity;

  @ManyToOne(() => ProductQuestEntity, { eager: false })
  @JoinColumn({ name: 'questId' })
  quest: ProductQuestEntity;
}
