import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DataSource,
  EntityManager,
  Repository,
  Between,
  LessThan,
  In,
  Not,
  IsNull,
} from 'typeorm';
import { CustomLogger } from '../src/common/logger/custom-logger.service';
import { NotificationsEntity } from '../src/models/notification-entity';
import { dailyQuestMessages } from './cron-utils/notification-messages.data';
import { CRON_TYPE } from './cron-utils/cron.data';
import { CronTableEntity } from '../src/models/cron-entity';
import { EnterpriseEntity, UserEntity } from '../src/models/user-entity';
import {
  QuestEntity,
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  SUBMISSION_MEDIA_TYPES,
  QuestTypesEntity,
} from '../src/models/quest-entity';
import { AWSLlamaAIService } from '../src/third-party/aws/llama/generate-quest/generate-quest.service';
import * as moment from 'moment';
import { AiQuestsGenerationLogEntity } from 'src/models/AI-quest-generation-log-entity';
import { ProductSummaryEntity } from '../src/models/pdf-parsing-entity';
import {
  ProductQuestEntity,
  PRODUCT_QUEST_SUBMISSION_TYPE,
  ProductQuestionAnswerEntity,
  ProductMCQQuestEntity,
  ProductQuestSubmissionEntity,
} from '../src/models/products-summary-entity';
import {
  ProductHuntMCQQuestPoolEntity,
  PRODUCT_HUNT_QUEST_DIFFICULTY,
} from '../src/models/quest-pool-entity/product-hunt-mcq-quest-pool.entity';
import { ProductHuntTextQuestPoolEntity } from '../src/models/quest-pool-entity/product-hunt-text-quest-pool.entity';

@Injectable()
export class CronService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly dataSource: DataSource,
    private readonly awsLlamaIService: AWSLlamaAIService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,
    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,
    @InjectRepository(CronTableEntity)
    private readonly cronTableRepo: Repository<CronTableEntity>,
    @InjectRepository(AiQuestsGenerationLogEntity)
    private readonly aiQuestsGenerationLogRepo: Repository<AiQuestsGenerationLogEntity>,
    @InjectRepository(ProductQuestEntity)
    private readonly productQuestRepo: Repository<ProductQuestEntity>,
    @InjectRepository(ProductMCQQuestEntity)
    private readonly productMCQQuestRepo: Repository<ProductMCQQuestEntity>,
    @InjectRepository(ProductQuestionAnswerEntity)
    private readonly productQuestionAnswerRepo: Repository<ProductQuestionAnswerEntity>,
    @InjectRepository(ProductSummaryEntity)
    private readonly productSummaryRepo: Repository<ProductSummaryEntity>,
    @InjectRepository(ProductQuestSubmissionEntity)
    private readonly productQuestSubmissionRepo: Repository<ProductQuestSubmissionEntity>,
    @InjectRepository(ProductHuntMCQQuestPoolEntity)
    private readonly productHuntQuestPoolRepo: Repository<ProductHuntMCQQuestPoolEntity>,
    @InjectRepository(ProductHuntTextQuestPoolEntity)
    private readonly productHuntTextQuestPoolRepo: Repository<ProductHuntTextQuestPoolEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly EnterpriseEntityRepo: Repository<EnterpriseEntity>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleQuestsCron() {
    await this.handleAIQuestGeneration();
    await this.handleEnterpriseQuestExpiry();
    await this.deactivateExpiredProductQuests();
    await this.generateProductHuntUniqueMCQ();
    await this.generateProductHuntUniqueQuestions();
    await this.assignProductHuntMCQQuestToUser();
    await this.assignProductHuntQuestionsQuestToUser();
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async handleUserNotifications() {
    console.log('\n\nRunning user notifications cron...');
    const cronType = CRON_TYPE.NOTIFICATION;

    try {
      await this.executeCronWithLock(cronType, async (manager) => {
        const notifications = await this.generateUserNotifications(manager);
        await manager.save(NotificationsEntity, notifications);
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async handleAIQuestGeneration(): Promise<void> {
    console.log('\n\nRunning AI Quest generation cron job...');
    const cronType = CRON_TYPE.AI_QUEST_GENERATION;

    try {
      const shouldProcess = await this.canProcessCron(cronType);
      if (!shouldProcess) {
        console.log('AI Quest generation already in progress...');
        return;
      }

      const cronEntry = await this.createNewCronTableEntry(cronType);
      const users = await this.getActiveUsers();

      for (const user of users) {
        if (user.selectedQuestTypes?.length) {
          await this.processUserAIQuests(user);
        }
      }

      await this.deleteCronTableEntry(cronEntry);
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async handleEnterpriseQuestExpiry(): Promise<void> {
    console.log('\n\nRunning Enterprise Quests Expiring cron...');
    const cronType = CRON_TYPE.EP_QUEST_EXPIRY;

    try {
      await this.executeCronWithLock(cronType, async (manager) => {
        const expiredQuests = await this.getExpiredEnterpriseQuests(manager);
        await this.deactivateQuests(expiredQuests, manager);
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async processUserAIQuests(user: UserEntity): Promise<void> {
    try {
      const dateRange = this.getDateRange();
      const existingQuests = await this.getExistingUserQuests(user, dateRange);
      const existingQuestTypes = new Set(
        existingQuests.map((quest) => quest.questType.id),
      );

      const canGenerateQuests = await this.canGenerateQuestsForUser(user);
      if (!canGenerateQuests) return;

      const logEntry = await this.createAIQuestGenerationLog(user);

      try {
        await this.generateNewQuestsForUser(user, existingQuestTypes);
      } finally {
        await this.aiQuestsGenerationLogRepo.delete(logEntry);
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async generateNewQuestsForUser(
    user: UserEntity,
    existingQuestTypes: Set<number>,
  ): Promise<void> {
    for (const questType of user.selectedQuestTypes) {
      if (existingQuestTypes.has(questType.id)) continue;

      const generatedQuests =
        await this.awsLlamaIService.generateQuestSuggestion(
          [questType],
          user.difficulty,
          user.workLocation,
        );

      if (generatedQuests?.length) {
        await this.createAndSaveAIQuests(generatedQuests, user);
      }
    }
  }

  private async createAndSaveAIQuests(
    AIQuests: any[],
    user: UserEntity,
  ): Promise<QuestEntity[]> {
    const quests = await Promise.all(
      AIQuests.map(async (item) => {
        try {
          return await this.createSingleAIQuest(item, user);
        } catch (error) {
          this.logger.error(error);
          return null;
        }
      }),
    );

    return quests.filter(Boolean);
  }

  private async createSingleAIQuest(
    item: any,
    user: UserEntity,
  ): Promise<QuestEntity> {
    const questType = await this.questTypeRepo.findOne({
      where: { value: item.questTypeValue },
    });

    if (!questType) {
      throw new Error('Invalid QuestType provided.');
    }

    const { startDate, endDate } = this.calculateQuestDates(item.isActive);

    const quest = new QuestEntity();
    Object.assign(quest, {
      scope: QUEST_SCOPE.AI,
      completionCredits: 50,
      title: item.questTitle,
      description: Array.isArray(item.questDescription)
        ? item.questDescription.join(', ')
        : item.questDescription,
      assignedToUser: user,
      workLocation: user.workLocation,
      submissionMediaType:
        questType.value === 'FITNESS_QUEST'
          ? SUBMISSION_MEDIA_TYPES.MIXED
          : questType.value === 'PHOTOGRAPHY_QUEST'
            ? SUBMISSION_MEDIA_TYPES.IMAGE
            : SUBMISSION_MEDIA_TYPES[item.proofOfCompletion.toUpperCase()],
      difficulty: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      isActive: item.isActive,
      startDate,
      endDate,
      questType,
      enterprise: user.enterprise,
    });

    return this.questRepo.save(quest);
  }

  private calculateQuestDates(isActive: boolean): {
    startDate: Date;
    endDate: Date;
  } {
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0);

    const nextStartDate = new Date(startDate);
    nextStartDate.setDate(nextStartDate.getDate() + 1);

    const nextEndDate = new Date(nextStartDate);
    nextEndDate.setDate(nextEndDate.getDate() + 1);

    return {
      startDate: isActive ? startDate : nextStartDate,
      endDate: isActive ? nextStartDate : nextEndDate,
    };
  }

  private async executeCronWithLock(
    cronType: CRON_TYPE,
    operation: (manager: EntityManager) => Promise<void>,
  ): Promise<void> {
    const cronEntry = await this.createNewCronTableEntry(cronType);

    try {
      await this.dataSource.transaction(async (manager) => {
        await operation(manager);
      });
    } finally {
      await this.deleteCronTableEntry(cronEntry);
    }
  }

  private async canProcessCron(cronType: CRON_TYPE): Promise<boolean> {
    const existingEntry = await this.cronTableRepo.findOne({
      where: { name: cronType },
    });
    return !existingEntry;
  }

  private async getActiveUsers(): Promise<UserEntity[]> {
    return this.userRepo.find({
      where: { isActive: true, isDeleted: false },
      relations: ['selectedQuestTypes', 'enterprise'],
    });
  }

  private getDateRange(): { start: Date; end: Date } {
    const start = new Date();
    start.setHours(0, 0, 0, 0);

    const end = new Date(start);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  private async getExistingUserQuests(
    user: UserEntity,
    dateRange: { start: Date; end: Date },
  ): Promise<QuestEntity[]> {
    return this.questRepo.find({
      where: {
        assignedToUser: { id: user.id },
        scope: QUEST_SCOPE.AI,
        startDate: Between(dateRange.start, dateRange.end),
      },
      relations: ['questType', 'assignedToUser', 'enterprise'],
    });
  }

  private async canGenerateQuestsForUser(user: UserEntity): Promise<boolean> {
    const currentDate = moment().format('YYYY-MM-DD');
    const existingLog = await this.aiQuestsGenerationLogRepo.findOne({
      where: {
        user: { id: user.id },
        currentDate,
      },
    });
    return !existingLog;
  }

  private async createAIQuestGenerationLog(
    user: UserEntity,
  ): Promise<AiQuestsGenerationLogEntity> {
    return this.aiQuestsGenerationLogRepo.save({
      user,
      currentDate: moment().format('YYYY-MM-DD'),
      workLocation: user.workLocation,
    });
  }

  private async getExpiredEnterpriseQuests(
    manager: EntityManager,
  ): Promise<QuestEntity[]> {
    return manager.find(QuestEntity, {
      where: {
        scope: QUEST_SCOPE.ENTERPRISE,
        endDate: LessThan(new Date()),
        isActive: true,
        isDeleted: false,
      },
    });
  }

  private async deactivateQuests(
    quests: QuestEntity[],
    manager: EntityManager,
  ): Promise<void> {
    for (const quest of quests) {
      quest.isActive = false;
      await manager.save(QuestEntity, quest);
    }
  }

  private async generateUserNotifications(
    manager: EntityManager,
  ): Promise<NotificationsEntity[]> {
    const completedQuests = await manager.find(QuestEntity, {
      where: {
        isCompleted: true,
        isDeleted: false,
      },
      relations: ['assignedToUser', 'assignedToUser.enterprise'],
    });

    const uniqueUsers = Array.from(
      new Map(
        completedQuests
          .filter((quest) => quest.assignedToUser)
          .map((quest) => [quest.assignedToUser.id, quest.assignedToUser]),
      ).values(),
    );

    return uniqueUsers.map((user) => {
      const notification = new NotificationsEntity();
      notification.content =
        dailyQuestMessages[
          Math.floor(Math.random() * dailyQuestMessages.length)
        ];
      notification.user = user;
      notification.enterprise = user.enterprise;
      return notification;
    });
  }

  private async createNewCronTableEntry(
    name: CRON_TYPE,
  ): Promise<CronTableEntity> {
    try {
      const entry = new CronTableEntity();
      entry.name = name;
      return await this.cronTableRepo.save(entry);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async deleteCronTableEntry(entry: CronTableEntity): Promise<void> {
    try {
      await this.cronTableRepo.remove(entry);
    } catch (error) {
      this.logger.error(error);
    }
  }

  // --- Product based mcq quest pool generation via cron job ---
  private normalizeQuestion(q: string): string {
    return q
      .toLowerCase()
      .replace(/[.,!?;:()\-]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private isSimilar(q1: string, q2: string): boolean {
    const words1 = new Set(q1.split(/\s+/));
    const words2 = new Set(q2.split(/\s+/));
    const overlap = [...words1].filter((w) => words2.has(w)).length;
    return overlap / Math.max(words1.size, words2.size) > 0.8;
  }

  private async generateProductHuntUniqueMCQ() {
    try {
      const productSummaries = await this.productSummaryRepo.find({
        where: {
          isEnableToView: true,
          isDeleted: false,
          content: Not(IsNull()),
        },
      });
      if (!productSummaries.length) {
        this.logger.warn('No product summaries found.');
        return;
      }
      for (const summary of productSummaries) {
        let totalSaved = 0;
        let attempts = 0;
        const maxAttempts = 10;
        const totalRun = 50; // 10 mcq each has 5 questions
        const seenQuestions: string[] = [];

        while (totalSaved < totalRun && attempts < maxAttempts) {
          // Fetch all previous MCQs for uniqueness
          const previousMCQ = await this.productHuntQuestPoolRepo.find({
            where: { productSummaryId: summary.id },
          });
          let aiBasedMCQ = null;
          try {
            if (summary.content) {
              aiBasedMCQ =
                await this.awsLlamaIService.generateUniqueMCQForProductHunt(
                  summary.content,
                  previousMCQ,
                );
            }
          } catch (e) {
            this.logger.warn(`Llama call failed: ${e.message}`);
            attempts++;
            continue;
          }
          if (
            !aiBasedMCQ ||
            !aiBasedMCQ.questions ||
            !Array.isArray(aiBasedMCQ.questions)
          ) {
            this.logger.warn('No valid MCQ object returned, retrying.');
            attempts++;
            continue;
          }
          // Normalize and filter for uniqueness
          const uniqueQuestions = (aiBasedMCQ.questions || []).filter(
            (q: any) => {
              const norm = this.normalizeQuestion(q.question);
              if (
                seenQuestions.some((s) => s === norm || this.isSimilar(s, norm))
              )
                return false;
              seenQuestions.push(norm);
              return true;
            },
          );
          for (const q of uniqueQuestions) {
            if (totalSaved >= totalRun) break;
            const entity = this.productHuntQuestPoolRepo.create({
              question: q.question,
              mcqOptions: q.options,
              correctAnswer: q.correctAnswer,
              difficulty: PRODUCT_HUNT_QUEST_DIFFICULTY.EASY,
              productSummaryId: summary.id,
            });
            try {
              await this.productHuntQuestPoolRepo.save(entity);
              totalSaved++;
            } catch (err) {
              this.logger.error({
                error: true,
                errorId: Date.now(),
                statusCode: 500,
                timestamp: new Date(),
                path: 'cron/generateTest',
                message: `Failed to save MCQ question: ${q.question}. Error: ${err && err.message ? err.message : err}`,
              });
            }
          }
          attempts++;
        }
      }
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: 'cron/generateTest',
        message: `Error in generateTest: ${error && error.message ? error.message : error}`,
      });
    }
  }

  private async generateProductHuntUniqueQuestions() {
    try {
      const productSummaries = await this.productSummaryRepo.find({
        where: {
          isEnableToView: true,
          isDeleted: false,
          content: Not(IsNull()),
        },
      });
      if (!productSummaries.length) {
        this.logger.warn('No product summaries found.');
        return;
      }

      for (const summary of productSummaries) {
        let totalSaved = 0;
        let attempts = 0;
        const totalQuestions = 10;
        while (totalSaved < totalQuestions) {
          // Fetch all previous Qs for uniqueness
          const previousQuestions =
            await this.productHuntTextQuestPoolRepo.find({
              where: { productSummaryId: summary.id },
            });
          let aiBasedQuestions = null;
          try {
            if (summary.content) {
              aiBasedQuestions =
                await this.awsLlamaIService.generateUniqueQuestionForProductHunt(
                  summary.content,
                  previousQuestions,
                );
            }
          } catch (e) {
            this.logger.warn(`Llama call failed: ${e.message}`);
            attempts++;
            continue;
          }
          if (!aiBasedQuestions || !aiBasedQuestions.question) {
            this.logger.warn('No valid Q&A array returned, retrying.');
            attempts++;
            continue;
          }
          // Filter for unique questions
          if (totalSaved >= totalQuestions) break;
          const entity = this.productHuntTextQuestPoolRepo.create({
            title: aiBasedQuestions.title || 'Product Hunt Q&A',
            question: aiBasedQuestions.question,
            answer: aiBasedQuestions.answer,
            difficulty: aiBasedQuestions.difficulty,
            productSummaryId: summary.id,
          });
          try {
            await this.productHuntTextQuestPoolRepo.save(entity);
            totalSaved++;
          } catch (err) {
            this.logger.error({
              error: true,
              errorId: Date.now(),
              statusCode: 500,
              timestamp: new Date(),
              path: 'cron/generateProductHuntUniqueQuestions',
              message: `Failed to save Q&A: ${aiBasedQuestions.question}. Error: ${err && err.message ? err.message : err}`,
            });
          }

          attempts++;
        }
      }
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: 'cron/generateProductHuntUniqueQuestions',
        message: `Error in generateProductHuntUniqueQuestions: ${error && error.message ? error.message : error}`,
      });
    }
  }

  private async assignProductHuntMCQQuestToUser() {
    try {
      const productHuntAllProductSummary = await this.productSummaryRepo.find({
        where: {
          isDeleted: false,
          isEnableToView: true,
          content: Not(IsNull()),
        },
      });

      for (const summary of productHuntAllProductSummary) {
        const findAllEnterprisesById = await this.EnterpriseEntityRepo.find({
          where: { id: summary.enterpriseId },
        });

        for (const enterprise of findAllEnterprisesById) {
          const findAllUsersBasedOnEnterprise = await this.userRepo.find({
            where: {
              enterprise: { id: enterprise.id },
              email: Not(process.env.APP_ADMIN_MAIL),
            },
          });

          for (const user of findAllUsersBasedOnEnterprise) {
            const latestAssignedQuest = await this.productQuestRepo.findOne({
              where: {
                assignToUserId: user.id,
                enterpriseId: enterprise.id,
                productSummaryId: summary.id,
              },
              order: {
                endDate: 'DESC', // Assumes your entity has a `endDate` column
              },
            });

            if (
              !latestAssignedQuest ||
              (latestAssignedQuest.submissionType === 'TEXT' &&
                this.isDateLessThanToday(
                  latestAssignedQuest.endDate.toString(),
                ))
            ) {
              // 1. Get all MCQs for this summary
              const allMCQs = await this.productHuntQuestPoolRepo.find({
                where: { productSummaryId: summary.id },
                order: { id: 'ASC' },
              });

              // 2. Find all ProductQuestEntity for this user, enterprise, and summary
              const userProductQuests = await this.productQuestRepo.find({
                where: {
                  assignToUserId: user.id,
                  enterpriseId: enterprise.id,
                  productSummaryId: summary.id,
                },
              });
              const userQuestIds = userProductQuests.map((q) => q.id);

              // 3. Find all ProductMCQQuestEntity for these questIds
              let alreadyAssignedQuestions = new Set<string>();
              if (userQuestIds.length > 0) {
                const assignedMCQs = await this.productMCQQuestRepo.find({
                  where: {
                    questId: In(userQuestIds),
                  },
                });
                alreadyAssignedQuestions = new Set(
                  assignedMCQs.map((mcq) => mcq.question),
                );
              }

              // 4. Filter out MCQs already assigned to this user
              const unassignedMCQs = allMCQs.filter(
                (mcq) => !alreadyAssignedQuestions.has(mcq.question),
              );

              // 5. Pick first 5 unassigned MCQs
              if (unassignedMCQs.length >= 5) {
                // 6. Create a new ProductQuestEntity for this assignment
                const questType = await this.questTypeRepo.findOne({
                  where: { value: 'PRODUCT_QUEST' },
                });
                const now = new Date();
                const endDate = new Date(now);
                endDate.setHours(23, 59, 0, 0);

                const quest = this.productQuestRepo.create({
                  title: '',
                  submissionType: PRODUCT_QUEST_SUBMISSION_TYPE.MCQ,
                  description: 'This is mcq based quest',
                  difficulty: 'easy',
                  completionCredits: 200,
                  startDate: now,
                  endDate,
                  isActive: true,
                  productSummaryId: summary.id,
                  enterpriseId: enterprise.id,
                  assignToUserId: user.id,
                  questTypeId: questType.id,
                });
                const savedQuest = await this.productQuestRepo.save(quest);

                const mcqQuestions = [];

                // 7. Assign the 5 MCQs to the user
                for (const mcq of unassignedMCQs.slice(0, 5)) {
                  const mcqEntity = this.productMCQQuestRepo.create({
                    question: mcq.question,
                    options: mcq.mcqOptions,
                    correctAnswers: [mcq.mcqOptions.indexOf(mcq.correctAnswer)],
                    difficulty: mcq.difficulty,
                    productSummaryId: summary.id,
                    enterpriseId: enterprise.id,
                    questId: savedQuest.id,
                    assignToUserId: { id: user.id },
                  });

                  const savedMCQQuest =
                    await this.productMCQQuestRepo.save(mcqEntity);
                  mcqQuestions.push(savedMCQQuest.question);
                }

                const { title, description } =
                  await this.awsLlamaIService.generateFourWordTitleAndDescriptionForProductHuntMCQ(
                    mcqQuestions,
                    summary.content,
                  );

                await this.productQuestRepo.update(savedQuest.id, {
                  title,
                  description,
                });
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: 'cron/generateProductHuntUniqueQuestions',
        message: `Error in generateProductHuntUniqueQuestions: ${error && error.message ? error.message : error}`,
      });
    }
  }

  private isDateLessThanToday(inputDateStr: string) {
    const parsedDate = new Date(inputDateStr);
    if (isNaN(parsedDate.getTime())) {
      throw new Error('Invalid date format');
    }

    // Get only the date part (UTC)
    const inputDateOnly = new Date(parsedDate.toISOString().split('T')[0]);
    const today = new Date();
    const todayOnly = new Date(today.toISOString().split('T')[0]);

    return inputDateOnly < todayOnly;
  }

  private async assignProductHuntQuestionsQuestToUser() {
    try {
      const productHuntAllProductSummary = await this.productSummaryRepo.find({
        where: {
          isDeleted: false,
          isEnableToView: true,
          content: Not(IsNull()),
        },
      });

      for (const summary of productHuntAllProductSummary) {
        const findAllEnterprisesById = await this.EnterpriseEntityRepo.find({
          where: { id: summary.enterpriseId },
        });

        for (const enterprise of findAllEnterprisesById) {
          const findAllUsersBasedOnEnterprise = await this.userRepo.find({
            where: {
              enterprise: { id: enterprise.id },
              email: Not(process.env.APP_ADMIN_MAIL),
            },
          });
          for (const user of findAllUsersBasedOnEnterprise) {
            // 1. Find all quests for this user, enterprise, and summary, ordered by endDate DESC
            const userProductQuests = await this.productQuestRepo.find({
              where: {
                assignToUserId: user.id,
                enterpriseId: enterprise.id,
                productSummaryId: summary.id,
              },
              order: { endDate: 'DESC' },
            });

            // 2. Check if any quest is assigned today
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const hasQuestToday = userProductQuests.some((q) => {
              const endDate =
                q.endDate instanceof Date ? q.endDate : new Date(q.endDate);
              return endDate >= today;
            });
            if (hasQuestToday) {
              continue;
            }

            // 3. Find the most recent quest before today
            const mostRecentQuestBeforeToday = userProductQuests.find((q) => {
              const endDate =
                q.endDate instanceof Date ? q.endDate : new Date(q.endDate);
              return endDate < today;
            });

            // 4. Assign TEXT only if:
            //    - No previous quest at all
            //    - OR last quest before today is MCQ
            if (
              !mostRecentQuestBeforeToday ||
              mostRecentQuestBeforeToday.submissionType ===
                PRODUCT_QUEST_SUBMISSION_TYPE.MCQ
            ) {
              // 5. Get all text questions for this summary
              const allTextQuestions =
                await this.productHuntTextQuestPoolRepo.find({
                  where: { productSummaryId: summary.id, isAssigned: false },
                  order: { id: 'ASC' },
                });

              // 6. Find all ProductQuestEntity for this user, enterprise, and summary
              const userQuestIds = userProductQuests.map((q) => q.id);

              // 7. Find all ProductQuestionAnswerEntity for these questIds
              let alreadyAssignedQuestions = new Set<string>();
              if (userQuestIds.length > 0) {
                const assignedTextQs =
                  await this.productQuestionAnswerRepo.find({
                    where: { productQuestId: In(userQuestIds) },
                  });
                alreadyAssignedQuestions = new Set(
                  assignedTextQs.map((qa) => qa.question),
                );
              }

              // 8. Filter out already assigned questions
              const unassignedQuestions = allTextQuestions.filter(
                (q) => !alreadyAssignedQuestions.has(q.question),
              );

              // 9. Pick the first unassigned question
              if (unassignedQuestions.length > 0) {
                const questionToAssign = unassignedQuestions[0];

                // 10. Generate a 5-word title for the quest (optional, can use question text)
                const groupTitle =
                  await this.awsLlamaIService.generateFourWordTitleForProductHuntQuestionAnswer(
                    questionToAssign.question,
                    summary.content,
                  );

                // 11. Get the quest type for product quests
                const questType = await this.questTypeRepo.findOne({
                  where: { value: 'PRODUCT_QUEST' },
                });
                const now = new Date();
                const endDate = new Date(now);
                endDate.setHours(23, 59, 0, 0);

                // 12. Create the ProductQuestEntity for the TEXT quest
                const quest = this.productQuestRepo.create({
                  title: groupTitle,
                  submissionType: PRODUCT_QUEST_SUBMISSION_TYPE.TEXT,
                  description: questionToAssign.question,
                  difficulty: questionToAssign.difficulty || 'easy',
                  completionCredits: 200,
                  startDate: now,
                  endDate,
                  isActive: true,
                  productSummaryId: summary.id,
                  enterpriseId: enterprise.id,
                  assignToUserId: user.id,
                  questTypeId: questType.id,
                });
                const savedQuest = await this.productQuestRepo.save(quest);

                // 13. Save the question in ProductQuestionAnswerEntity
                const qaEntity = this.productQuestionAnswerRepo.create({
                  question: questionToAssign.question,
                  answer: questionToAssign.answer,
                  productQuestId: savedQuest.id,
                });
                await this.productQuestionAnswerRepo.save(qaEntity);

                // 14. Mark the question as assigned in the pool
                await this.productHuntTextQuestPoolRepo.update(
                  { id: questionToAssign.id },
                  { isAssigned: true },
                );
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: 'cron/assignProductHuntQuestionsQuestToUser',
        message: `Error in assignProductHuntQuestionsQuestToUser: ${error && error.message ? error.message : error}`,
      });
    }
  }

  // Restore the deactivateExpiredProductQuests method
  private async deactivateExpiredProductQuests() {
    try {
      const today = new Date();
      const todayStr = today.toISOString().slice(0, 10); // 'YYYY-MM-DD'
      const data = await this.productQuestRepo
        .createQueryBuilder()
        .update()
        .set({ isActive: false })
        .where('endDate < :today', { today: todayStr })
        .andWhere('isActive = :active', { active: true })
        .execute();
    } catch (error) {
      this.logger.error({
        error: true,
        errorId: Date.now(),
        statusCode: 500,
        timestamp: new Date(),
        path: 'cron/deactivateExpiredProductQuests',
        message: `Error in deactivateExpiredProductQuests: ${error && error.message ? error.message : error}`,
      });
    }
  }
}
